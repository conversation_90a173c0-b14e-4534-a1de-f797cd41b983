-- سكريبت إنشاء قاعدة بيانات نظام محاسبة وزارة الشباب والرياضة
-- Ministry of Youth and Sports Accounting System Database Creation Script

USE master;
GO

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'MinistryAccountingDB')
BEGIN
    CREATE DATABASE [MinistryAccountingDB]
    ON 
    ( NAME = N'MinistryAccountingDB', 
      FILENAME = N'C:\MinistryAccounting\Database\MinistryAccountingDB.mdf',
      SIZE = 100MB, 
      MAXSIZE = 1GB, 
      FILEGROWTH = 10MB )
    LOG ON 
    ( NAME = N'MinistryAccountingDB_Log', 
      FILENAME = N'C:\MinistryAccounting\Database\MinistryAccountingDB_Log.ldf',
      SIZE = 10MB, 
      MAXSIZE = 100MB, 
      FILEGROWTH = 10% );
END
GO

USE [MinistryAccountingDB];
GO

-- إنشاء مخطط للنظام
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = N'Ministry')
BEGIN
    EXEC('CREATE SCHEMA [Ministry]');
END
GO

-- ========================================
-- جداول إدارة المستخدمين
-- ========================================

-- جدول مجموعات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserGroups' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserGroups] (
        [UserGroupId] INT IDENTITY(1,1) PRIMARY KEY,
        [GroupName] NVARCHAR(100) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users] (
        [UserId] INT IDENTITY(1,1) PRIMARY KEY,
        [Username] NVARCHAR(100) NOT NULL UNIQUE,
        [FullName] NVARCHAR(200) NOT NULL,
        [Email] NVARCHAR(150) NOT NULL UNIQUE,
        [PasswordHash] NVARCHAR(255) NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        [LastLoginDate] DATETIME NULL,
        [FailedLoginAttempts] INT NOT NULL DEFAULT 0,
        [UserGroupId] INT NULL,
        FOREIGN KEY ([UserGroupId]) REFERENCES [UserGroups]([UserGroupId])
    );
END
GO

-- جدول الصلاحيات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Permissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Permissions] (
        [PermissionId] INT IDENTITY(1,1) PRIMARY KEY,
        [PermissionName] NVARCHAR(100) NOT NULL,
        [ModuleName] NVARCHAR(100) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );
END
GO

-- جدول صلاحيات المستخدمين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserPermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserPermissions] (
        [UserPermissionId] INT IDENTITY(1,1) PRIMARY KEY,
        [UserId] INT NOT NULL,
        [PermissionId] INT NOT NULL,
        [CanRead] BIT NOT NULL DEFAULT 0,
        [CanAdd] BIT NOT NULL DEFAULT 0,
        [CanEdit] BIT NOT NULL DEFAULT 0,
        [CanDelete] BIT NOT NULL DEFAULT 0,
        [GrantedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY ([UserId]) REFERENCES [Users]([UserId]) ON DELETE CASCADE,
        FOREIGN KEY ([PermissionId]) REFERENCES [Permissions]([PermissionId]) ON DELETE CASCADE
    );
END
GO

-- جدول صلاحيات المجموعات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='GroupPermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[GroupPermissions] (
        [GroupPermissionId] INT IDENTITY(1,1) PRIMARY KEY,
        [UserGroupId] INT NOT NULL,
        [PermissionId] INT NOT NULL,
        [CanRead] BIT NOT NULL DEFAULT 0,
        [CanAdd] BIT NOT NULL DEFAULT 0,
        [CanEdit] BIT NOT NULL DEFAULT 0,
        [CanDelete] BIT NOT NULL DEFAULT 0,
        [GrantedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY ([UserGroupId]) REFERENCES [UserGroups]([UserGroupId]) ON DELETE CASCADE,
        FOREIGN KEY ([PermissionId]) REFERENCES [Permissions]([PermissionId]) ON DELETE CASCADE
    );
END
GO

-- ========================================
-- جداول الإعدادات والبيانات الأساسية
-- ========================================

-- جدول معلومات المؤسسة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrganizationInfo' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[OrganizationInfo] (
        [OrganizationId] INT IDENTITY(1,1) PRIMARY KEY,
        [OrganizationName] NVARCHAR(200) NOT NULL,
        [OrganizationNameEn] NVARCHAR(200) NULL,
        [Address] NVARCHAR(500) NULL,
        [Phone] NVARCHAR(50) NULL,
        [Fax] NVARCHAR(50) NULL,
        [Email] NVARCHAR(150) NULL,
        [Website] NVARCHAR(100) NULL,
        [Logo] VARBINARY(MAX) NULL,
        [LastUpdated] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول الدوائر
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Departments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Departments] (
        [DepartmentId] INT IDENTITY(1,1) PRIMARY KEY,
        [DepartmentCode] NVARCHAR(10) NOT NULL UNIQUE,
        [DepartmentName] NVARCHAR(200) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول الأقسام
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sections' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Sections] (
        [SectionId] INT IDENTITY(1,1) PRIMARY KEY,
        [SectionCode] NVARCHAR(10) NOT NULL,
        [SectionName] NVARCHAR(200) NOT NULL,
        [DepartmentId] INT NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY ([DepartmentId]) REFERENCES [Departments]([DepartmentId])
    );
END
GO

-- جدول الشعب
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Divisions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Divisions] (
        [DivisionId] INT IDENTITY(1,1) PRIMARY KEY,
        [DivisionCode] NVARCHAR(10) NOT NULL,
        [DivisionName] NVARCHAR(200) NOT NULL,
        [SectionId] INT NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY ([SectionId]) REFERENCES [Sections]([SectionId])
    );
END
GO

-- جدول المصارف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Banks' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Banks] (
        [BankId] INT IDENTITY(1,1) PRIMARY KEY,
        [BankCode] NVARCHAR(10) NOT NULL UNIQUE,
        [BankName] NVARCHAR(200) NOT NULL,
        [BankNameEn] NVARCHAR(200) NULL,
        [Address] NVARCHAR(500) NULL,
        [Phone] NVARCHAR(50) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول فروع المصارف
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BankBranches' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[BankBranches] (
        [BranchId] INT IDENTITY(1,1) PRIMARY KEY,
        [BranchCode] NVARCHAR(10) NOT NULL,
        [BranchName] NVARCHAR(200) NOT NULL,
        [BankId] INT NOT NULL,
        [Address] NVARCHAR(500) NULL,
        [Phone] NVARCHAR(50) NULL,
        [OperationalAccount] NVARCHAR(50) NULL,
        [PayrollAccount] NVARCHAR(50) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY ([BankId]) REFERENCES [Banks]([BankId])
    );
END
GO

-- جدول الصناديق
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CashBoxes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CashBoxes] (
        [CashBoxId] INT IDENTITY(1,1) PRIMARY KEY,
        [CashBoxCode] NVARCHAR(10) NOT NULL UNIQUE,
        [CashBoxName] NVARCHAR(200) NOT NULL,
        [Description] NVARCHAR(500) NULL,
        [CurrentBalance] DECIMAL(18,3) NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- جدول العملات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Currencies' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Currencies] (
        [CurrencyId] INT IDENTITY(1,1) PRIMARY KEY,
        [CurrencyCode] NVARCHAR(3) NOT NULL UNIQUE,
        [CurrencyName] NVARCHAR(100) NOT NULL,
        [CurrencyNameEn] NVARCHAR(100) NULL,
        [Symbol] NVARCHAR(10) NULL,
        [ExchangeRate] DECIMAL(18,6) NOT NULL DEFAULT 1,
        [IsDefault] BIT NOT NULL DEFAULT 0,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
    );
END
GO

-- إدراج البيانات الأساسية
-- إدراج معلومات المؤسسة
IF NOT EXISTS (SELECT * FROM [OrganizationInfo])
BEGIN
    INSERT INTO [OrganizationInfo] ([OrganizationName], [OrganizationNameEn])
    VALUES (N'وزارة الشباب والرياضة', N'Ministry of Youth and Sports');
END
GO

-- إدراج العملة الافتراضية
IF NOT EXISTS (SELECT * FROM [Currencies] WHERE [CurrencyCode] = 'IQD')
BEGIN
    INSERT INTO [Currencies] ([CurrencyCode], [CurrencyName], [CurrencyNameEn], [Symbol], [IsDefault])
    VALUES ('IQD', N'دينار عراقي', 'Iraqi Dinar', N'د.ع', 1);
END
GO

-- إدراج مجموعة المدراء
IF NOT EXISTS (SELECT * FROM [UserGroups] WHERE [GroupName] = N'المدراء')
BEGIN
    INSERT INTO [UserGroups] ([GroupName], [Description])
    VALUES (N'المدراء', N'مجموعة المدراء مع صلاحيات كاملة');
END
GO

-- إدراج المستخدم الافتراضي (admin)
IF NOT EXISTS (SELECT * FROM [Users] WHERE [Username] = 'admin')
BEGIN
    DECLARE @AdminGroupId INT = (SELECT TOP 1 [UserGroupId] FROM [UserGroups] WHERE [GroupName] = N'المدراء');
    
    INSERT INTO [Users] ([Username], [FullName], [Email], [PasswordHash], [UserGroupId])
    VALUES ('admin', N'مدير النظام', '<EMAIL>', 
            'jGl25bVBBBW96Qi9Te4V37Fnqchz/Eu4qB9vKrRIqRg=', -- كلمة المرور: admin123
            @AdminGroupId);
END
GO

PRINT 'تم إنشاء قاعدة البيانات بنجاح - Database created successfully';
GO
