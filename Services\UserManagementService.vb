Imports System.Data.Entity
Imports System.Security.Cryptography
Imports System.Text
Imports MinistryAccountingSystem.Models.UserManagement
Imports MinistryAccountingSystem.DataAccess

Namespace Services

    ''' <summary>
    ''' خدمة إدارة المستخدمين والصلاحيات
    ''' </summary>
    Public Class UserManagementService
        Inherits BaseService

        ''' <summary>
        ''' منشئ خدمة إدارة المستخدمين
        ''' </summary>
        Public Sub New()
            MyBase.New()
        End Sub

        ''' <summary>
        ''' منشئ خدمة إدارة المستخدمين مع سياق مخصص
        ''' </summary>
        ''' <param name="context">سياق قاعدة البيانات</param>
        Public Sub New(context As MinistryDbContext)
            MyBase.New(context)
        End Sub

        #Region "User Management"

        ''' <summary>
        ''' إنشاء مستخدم جديد
        ''' </summary>
        ''' <param name="user">بيانات المستخدم</param>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>نتيجة العملية</returns>
        Public Function CreateUser(user As User, password As String) As ServiceResult(Of User)
            Try
                ' التحقق من صحة البيانات
                Dim validationErrors = ValidateUser(user, password)
                If validationErrors.Any() Then
                    Return ServiceResult(Of User).Failure("بيانات غير صحيحة", validationErrors)
                End If

                ' التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
                If _context.Users.Any(Function(u) u.Username = user.Username) Then
                    Return ServiceResult(Of User).Failure("اسم المستخدم موجود مسبقاً")
                End If

                If _context.Users.Any(Function(u) u.Email = user.Email) Then
                    Return ServiceResult(Of User).Failure("البريد الإلكتروني موجود مسبقاً")
                End If

                ' تشفير كلمة المرور
                user.PasswordHash = HashPassword(password)
                user.CreatedDate = DateTime.Now
                user.IsActive = True

                _context.Users.Add(user)
                SaveChanges()

                LogInfo($"تم إنشاء مستخدم جديد: {user.Username}")
                Return ServiceResult(Of User).Success(user, "تم إنشاء المستخدم بنجاح")

            Catch ex As Exception
                LogError("خطأ في إنشاء المستخدم", ex)
                Return ServiceResult(Of User).Failure("حدث خطأ أثناء إنشاء المستخدم")
            End Try
        End Function

        ''' <summary>
        ''' تحديث بيانات المستخدم
        ''' </summary>
        ''' <param name="user">بيانات المستخدم المحدثة</param>
        ''' <returns>نتيجة العملية</returns>
        Public Function UpdateUser(user As User) As ServiceResult(Of User)
            Try
                Dim existingUser = _context.Users.Find(user.UserId)
                If existingUser Is Nothing Then
                    Return ServiceResult(Of User).Failure("المستخدم غير موجود")
                End If

                ' التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
                If _context.Users.Any(Function(u) u.Username = user.Username AndAlso u.UserId <> user.UserId) Then
                    Return ServiceResult(Of User).Failure("اسم المستخدم موجود مسبقاً")
                End If

                If _context.Users.Any(Function(u) u.Email = user.Email AndAlso u.UserId <> user.UserId) Then
                    Return ServiceResult(Of User).Failure("البريد الإلكتروني موجود مسبقاً")
                End If

                ' تحديث البيانات
                existingUser.Username = user.Username
                existingUser.FullName = user.FullName
                existingUser.Email = user.Email
                existingUser.IsActive = user.IsActive
                existingUser.UserGroupId = user.UserGroupId

                SaveChanges()

                LogInfo($"تم تحديث بيانات المستخدم: {user.Username}")
                Return ServiceResult(Of User).Success(existingUser, "تم تحديث بيانات المستخدم بنجاح")

            Catch ex As Exception
                LogError("خطأ في تحديث المستخدم", ex)
                Return ServiceResult(Of User).Failure("حدث خطأ أثناء تحديث المستخدم")
            End Try
        End Function

        ''' <summary>
        ''' حذف مستخدم
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <returns>نتيجة العملية</returns>
        Public Function DeleteUser(userId As Integer) As ServiceResult
            Try
                Dim user = _context.Users.Find(userId)
                If user Is Nothing Then
                    Return ServiceResult.Failure("المستخدم غير موجود")
                End If

                ' حذف صلاحيات المستخدم أولاً
                Dim userPermissions = _context.UserPermissions.Where(Function(up) up.UserId = userId).ToList()
                _context.UserPermissions.RemoveRange(userPermissions)

                ' حذف المستخدم
                _context.Users.Remove(user)
                SaveChanges()

                LogInfo($"تم حذف المستخدم: {user.Username}")
                Return ServiceResult.Success("تم حذف المستخدم بنجاح")

            Catch ex As Exception
                LogError("خطأ في حذف المستخدم", ex)
                Return ServiceResult.Failure("حدث خطأ أثناء حذف المستخدم")
            End Try
        End Function

        ''' <summary>
        ''' الحصول على جميع المستخدمين
        ''' </summary>
        ''' <returns>قائمة المستخدمين</returns>
        Public Function GetAllUsers() As ServiceResult(Of List(Of User))
            Try
                Dim users = _context.Users.Include(Function(u) u.UserGroup).ToList()
                Return ServiceResult(Of List(Of User)).Success(users)
            Catch ex As Exception
                LogError("خطأ في الحصول على المستخدمين", ex)
                Return ServiceResult(Of List(Of User)).Failure("حدث خطأ أثناء الحصول على المستخدمين")
            End Try
        End Function

        ''' <summary>
        ''' الحصول على مستخدم بالمعرف
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <returns>بيانات المستخدم</returns>
        Public Function GetUserById(userId As Integer) As ServiceResult(Of User)
            Try
                Dim user = _context.Users.Include(Function(u) u.UserGroup).FirstOrDefault(Function(u) u.UserId = userId)
                If user Is Nothing Then
                    Return ServiceResult(Of User).Failure("المستخدم غير موجود")
                End If

                Return ServiceResult(Of User).Success(user)
            Catch ex As Exception
                LogError("خطأ في الحصول على المستخدم", ex)
                Return ServiceResult(Of User).Failure("حدث خطأ أثناء الحصول على المستخدم")
            End Try
        End Function

        ''' <summary>
        ''' تسجيل دخول المستخدم
        ''' </summary>
        ''' <param name="username">اسم المستخدم</param>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>نتيجة تسجيل الدخول</returns>
        Public Function Login(username As String, password As String) As ServiceResult(Of User)
            Try
                Dim user = _context.Users.Include(Function(u) u.UserGroup).FirstOrDefault(Function(u) u.Username = username)
                
                If user Is Nothing Then
                    Return ServiceResult(Of User).Failure("اسم المستخدم أو كلمة المرور غير صحيحة")
                End If

                If Not user.IsActive Then
                    Return ServiceResult(Of User).Failure("حساب المستخدم غير نشط")
                End If

                If user.FailedLoginAttempts >= 3 Then
                    Return ServiceResult(Of User).Failure("تم حظر الحساب بسبب محاولات تسجيل دخول فاشلة متعددة")
                End If

                If Not VerifyPassword(password, user.PasswordHash) Then
                    user.FailedLoginAttempts += 1
                    SaveChanges()
                    Return ServiceResult(Of User).Failure("اسم المستخدم أو كلمة المرور غير صحيحة")
                End If

                ' تسجيل دخول ناجح
                user.LastLoginDate = DateTime.Now
                user.FailedLoginAttempts = 0
                SaveChanges()

                LogInfo($"تسجيل دخول ناجح للمستخدم: {username}")
                Return ServiceResult(Of User).Success(user, "تم تسجيل الدخول بنجاح")

            Catch ex As Exception
                LogError("خطأ في تسجيل الدخول", ex)
                Return ServiceResult(Of User).Failure("حدث خطأ أثناء تسجيل الدخول")
            End Try
        End Function

        ''' <summary>
        ''' تغيير كلمة المرور
        ''' </summary>
        ''' <param name="userId">معرف المستخدم</param>
        ''' <param name="oldPassword">كلمة المرور القديمة</param>
        ''' <param name="newPassword">كلمة المرور الجديدة</param>
        ''' <returns>نتيجة العملية</returns>
        Public Function ChangePassword(userId As Integer, oldPassword As String, newPassword As String) As ServiceResult
            Try
                Dim user = _context.Users.Find(userId)
                If user Is Nothing Then
                    Return ServiceResult.Failure("المستخدم غير موجود")
                End If

                If Not VerifyPassword(oldPassword, user.PasswordHash) Then
                    Return ServiceResult.Failure("كلمة المرور القديمة غير صحيحة")
                End If

                If String.IsNullOrWhiteSpace(newPassword) OrElse newPassword.Length < 8 Then
                    Return ServiceResult.Failure("كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل")
                End If

                user.PasswordHash = HashPassword(newPassword)
                SaveChanges()

                LogInfo($"تم تغيير كلمة المرور للمستخدم: {user.Username}")
                Return ServiceResult.Success("تم تغيير كلمة المرور بنجاح")

            Catch ex As Exception
                LogError("خطأ في تغيير كلمة المرور", ex)
                Return ServiceResult.Failure("حدث خطأ أثناء تغيير كلمة المرور")
            End Try
        End Function

        #End Region

        #Region "Helper Methods"

        ''' <summary>
        ''' التحقق من صحة بيانات المستخدم
        ''' </summary>
        ''' <param name="user">المستخدم</param>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>قائمة الأخطاء</returns>
        Private Function ValidateUser(user As User, password As String) As List(Of String)
            Dim errors As New List(Of String)

            If String.IsNullOrWhiteSpace(user.Username) Then
                errors.Add("اسم المستخدم مطلوب")
            End If

            If String.IsNullOrWhiteSpace(user.FullName) Then
                errors.Add("الاسم الكامل مطلوب")
            End If

            If String.IsNullOrWhiteSpace(user.Email) Then
                errors.Add("البريد الإلكتروني مطلوب")
            End If

            If String.IsNullOrWhiteSpace(password) OrElse password.Length < 8 Then
                errors.Add("كلمة المرور يجب أن تكون 8 أحرف على الأقل")
            End If

            Return errors
        End Function

        ''' <summary>
        ''' تشفير كلمة المرور
        ''' </summary>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>كلمة المرور المشفرة</returns>
        Private Function HashPassword(password As String) As String
            Using sha256 As SHA256 = SHA256.Create()
                Dim hashedBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "MinistryAccountingSalt"))
                Return Convert.ToBase64String(hashedBytes)
            End Using
        End Function

        ''' <summary>
        ''' التحقق من كلمة المرور
        ''' </summary>
        ''' <param name="password">كلمة المرور</param>
        ''' <param name="hashedPassword">كلمة المرور المشفرة</param>
        ''' <returns>true إذا كانت كلمة المرور صحيحة</returns>
        Private Function VerifyPassword(password As String, hashedPassword As String) As Boolean
            Dim hashedInput As String = HashPassword(password)
            Return hashedInput = hashedPassword
        End Function

        #End Region

    End Class

End Namespace
