Imports System.Security.Cryptography
Imports System.Text
Imports System.IO

Namespace Helpers

    ''' <summary>
    ''' مساعد الأمان والتشفير
    ''' </summary>
    Public Class SecurityHelper

        Private Const SALT As String = "MinistryAccountingSalt2024"
        Private Const ENCRYPTION_KEY As String = "MinistryYouthSports2024Key"

        ''' <summary>
        ''' تشفير كلمة المرور باستخدام SHA256
        ''' </summary>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>كلمة المرور المشفرة</returns>
        Public Shared Function HashPassword(password As String) As String
            If String.IsNullOrEmpty(password) Then
                Throw New ArgumentException("كلمة المرور لا يمكن أن تكون فارغة")
            End If

            Using sha256 As SHA256 = SHA256.Create()
                Dim saltedPassword As String = password + SALT
                Dim hashedBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword))
                Return Convert.ToBase64String(hashedBytes)
            End Using
        End Function

        ''' <summary>
        ''' التحقق من كلمة المرور
        ''' </summary>
        ''' <param name="password">كلمة المرور المدخلة</param>
        ''' <param name="hashedPassword">كلمة المرور المشفرة المحفوظة</param>
        ''' <returns>true إذا كانت كلمة المرور صحيحة</returns>
        Public Shared Function VerifyPassword(password As String, hashedPassword As String) As Boolean
            If String.IsNullOrEmpty(password) OrElse String.IsNullOrEmpty(hashedPassword) Then
                Return False
            End If

            Try
                Dim hashedInput As String = HashPassword(password)
                Return hashedInput.Equals(hashedPassword, StringComparison.Ordinal)
            Catch
                Return False
            End Try
        End Function

        ''' <summary>
        ''' توليد كلمة مرور عشوائية
        ''' </summary>
        ''' <param name="length">طول كلمة المرور</param>
        ''' <returns>كلمة مرور عشوائية</returns>
        Public Shared Function GenerateRandomPassword(Optional length As Integer = 12) As String
            Const chars As String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*"
            Dim random As New Random()
            Dim result As New StringBuilder(length)

            For i As Integer = 0 To length - 1
                result.Append(chars(random.Next(chars.Length)))
            Next

            Return result.ToString()
        End Function

        ''' <summary>
        ''' تشفير النص باستخدام AES
        ''' </summary>
        ''' <param name="plainText">النص المراد تشفيره</param>
        ''' <returns>النص المشفر</returns>
        Public Shared Function EncryptText(plainText As String) As String
            If String.IsNullOrEmpty(plainText) Then
                Return String.Empty
            End If

            Try
                Using aes As Aes = Aes.Create()
                    aes.Key = Encoding.UTF8.GetBytes(ENCRYPTION_KEY.PadRight(32).Substring(0, 32))
                    aes.IV = New Byte(15) {} ' IV بطول 16 بايت مملوء بالأصفار

                    Using encryptor As ICryptoTransform = aes.CreateEncryptor(aes.Key, aes.IV)
                        Using msEncrypt As New MemoryStream()
                            Using csEncrypt As New CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write)
                                Using swEncrypt As New StreamWriter(csEncrypt)
                                    swEncrypt.Write(plainText)
                                End Using
                            End Using
                            Return Convert.ToBase64String(msEncrypt.ToArray())
                        End Using
                    End Using
                End Using
            Catch ex As Exception
                Throw New Exception($"خطأ في تشفير النص: {ex.Message}")
            End Try
        End Function

        ''' <summary>
        ''' فك تشفير النص باستخدام AES
        ''' </summary>
        ''' <param name="cipherText">النص المشفر</param>
        ''' <returns>النص الأصلي</returns>
        Public Shared Function DecryptText(cipherText As String) As String
            If String.IsNullOrEmpty(cipherText) Then
                Return String.Empty
            End If

            Try
                Using aes As Aes = Aes.Create()
                    aes.Key = Encoding.UTF8.GetBytes(ENCRYPTION_KEY.PadRight(32).Substring(0, 32))
                    aes.IV = New Byte(15) {} ' IV بطول 16 بايت مملوء بالأصفار

                    Using decryptor As ICryptoTransform = aes.CreateDecryptor(aes.Key, aes.IV)
                        Using msDecrypt As New MemoryStream(Convert.FromBase64String(cipherText))
                            Using csDecrypt As New CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read)
                                Using srDecrypt As New StreamReader(csDecrypt)
                                    Return srDecrypt.ReadToEnd()
                                End Using
                            End Using
                        End Using
                    End Using
                End Using
            Catch ex As Exception
                Throw New Exception($"خطأ في فك تشفير النص: {ex.Message}")
            End Try
        End Function

        ''' <summary>
        ''' توليد رمز مميز (Token) عشوائي
        ''' </summary>
        ''' <param name="length">طول الرمز</param>
        ''' <returns>رمز مميز عشوائي</returns>
        Public Shared Function GenerateToken(Optional length As Integer = 32) As String
            Using rng As RandomNumberGenerator = RandomNumberGenerator.Create()
                Dim tokenBytes(length - 1) As Byte
                rng.GetBytes(tokenBytes)
                Return Convert.ToBase64String(tokenBytes).Replace("+", "").Replace("/", "").Replace("=", "").Substring(0, length)
            End Using
        End Function

        ''' <summary>
        ''' التحقق من قوة كلمة المرور
        ''' </summary>
        ''' <param name="password">كلمة المرور</param>
        ''' <returns>مستوى قوة كلمة المرور</returns>
        Public Shared Function CheckPasswordStrength(password As String) As PasswordStrength
            If String.IsNullOrEmpty(password) Then
                Return PasswordStrength.VeryWeak
            End If

            Dim score As Integer = 0

            ' طول كلمة المرور
            If password.Length >= 8 Then score += 1
            If password.Length >= 12 Then score += 1

            ' وجود أحرف صغيرة
            If password.Any(Function(c) Char.IsLower(c)) Then score += 1

            ' وجود أحرف كبيرة
            If password.Any(Function(c) Char.IsUpper(c)) Then score += 1

            ' وجود أرقام
            If password.Any(Function(c) Char.IsDigit(c)) Then score += 1

            ' وجود رموز خاصة
            If password.Any(Function(c) "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)) Then score += 1

            Select Case score
                Case 0 To 2
                    Return PasswordStrength.VeryWeak
                Case 3
                    Return PasswordStrength.Weak
                Case 4
                    Return PasswordStrength.Medium
                Case 5
                    Return PasswordStrength.Strong
                Case 6
                    Return PasswordStrength.VeryStrong
                Case Else
                    Return PasswordStrength.VeryWeak
            End Select
        End Function

        ''' <summary>
        ''' تنظيف البيانات الحساسة من الذاكرة
        ''' </summary>
        ''' <param name="sensitiveData">البيانات الحساسة</param>
        Public Shared Sub ClearSensitiveData(ByRef sensitiveData As String)
            If Not String.IsNullOrEmpty(sensitiveData) Then
                sensitiveData = New String("*"c, sensitiveData.Length)
                sensitiveData = Nothing
                GC.Collect()
                GC.WaitForPendingFinalizers()
            End If
        End Sub

        ''' <summary>
        ''' تسجيل محاولة دخول مشبوهة
        ''' </summary>
        ''' <param name="username">اسم المستخدم</param>
        ''' <param name="ipAddress">عنوان IP</param>
        ''' <param name="reason">سبب الشك</param>
        Public Shared Sub LogSuspiciousActivity(username As String, ipAddress As String, reason As String)
            Try
                Dim logPath As String = "C:\MinistryAccounting\Logs\security.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - نشاط مشبوه - المستخدم: {username}, IP: {ipAddress}, السبب: {reason}{Environment.NewLine}"
                
                ' إنشاء المجلد إذا لم يكن موجوداً
                Dim logDirectory As String = Path.GetDirectoryName(logPath)
                If Not Directory.Exists(logDirectory) Then
                    Directory.CreateDirectory(logDirectory)
                End If

                File.AppendAllText(logPath, logEntry, Encoding.UTF8)
            Catch
                ' تجاهل أخطاء التسجيل
            End Try
        End Sub

        ''' <summary>
        ''' التحقق من صحة عنوان البريد الإلكتروني
        ''' </summary>
        ''' <param name="email">عنوان البريد الإلكتروني</param>
        ''' <returns>true إذا كان العنوان صحيحاً</returns>
        Public Shared Function IsValidEmail(email As String) As Boolean
            If String.IsNullOrWhiteSpace(email) Then
                Return False
            End If

            Try
                Dim emailRegex As New System.Text.RegularExpressions.Regex("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
                Return emailRegex.IsMatch(email)
            Catch
                Return False
            End Try
        End Function

        ''' <summary>
        ''' تنظيف النص من الأحرف الخطيرة (منع SQL Injection)
        ''' </summary>
        ''' <param name="input">النص المدخل</param>
        ''' <returns>النص المنظف</returns>
        Public Shared Function SanitizeInput(input As String) As String
            If String.IsNullOrEmpty(input) Then
                Return String.Empty
            End If

            ' إزالة الأحرف الخطيرة
            Dim dangerousChars() As String = {"'", """", ";", "--", "/*", "*/", "xp_", "sp_", "DROP", "DELETE", "INSERT", "UPDATE", "SELECT", "UNION", "EXEC"}
            
            Dim sanitized As String = input
            For Each dangerousChar As String In dangerousChars
                sanitized = sanitized.Replace(dangerousChar, "", StringComparison.OrdinalIgnoreCase)
            Next

            Return sanitized.Trim()
        End Function

    End Class

    ''' <summary>
    ''' مستويات قوة كلمة المرور
    ''' </summary>
    Public Enum PasswordStrength
        VeryWeak = 0
        Weak = 1
        Medium = 2
        Strong = 3
        VeryStrong = 4
    End Enum

End Namespace
