Imports DevExpress.XtraEditors
Imports DevExpress.XtraNavBar
Imports DevExpress.XtraTabbedMdi
Imports System.Globalization
Imports System.Threading
Imports MinistryAccountingSystem.Models.UserManagement
Imports MinistryAccountingSystem.Services

''' <summary>
''' النموذج الرئيسي لنظام محاسبة وزارة الشباب والرياضة
''' </summary>
Public Class MainForm
    Inherits XtraForm

    Private WithEvents navBarControl As NavBarControl
    Private WithEvents tabbedMdiManager As XtraTabbedMdiManager
    Private currentUser As User
    Private userService As UserManagementService

    ''' <summary>
    ''' منشئ النموذج الرئيسي
    ''' </summary>
    Public Sub New()
        InitializeComponent()
        InitializeArabicSupport()
        InitializeNavigation()
        InitializeMDI()
        userService = New UserManagementService()
    End Sub

    ''' <summary>
    ''' منشئ النموذج الرئيسي مع المستخدم الحالي
    ''' </summary>
    ''' <param name="user">المستخدم الحالي</param>
    Public Sub New(user As User)
        Me.New()
        currentUser = user
        SetupUserInterface()
    End Sub

    ''' <summary>
    ''' تهيئة المكونات
    ''' </summary>
    Private Sub InitializeComponent()
        Me.SuspendLayout()
        
        ' إعدادات النموذج الرئيسي
        Me.Text = "نظام محاسبة وزارة الشباب والرياضة"
        Me.WindowState = FormWindowState.Maximized
        Me.IsMdiContainer = True
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Icon = My.Resources.AppIcon ' يجب إضافة أيقونة التطبيق

        ' شريط الحالة
        Dim statusStrip As New StatusStrip()
        statusStrip.RightToLeft = RightToLeft.Yes
        statusStrip.Items.Add(New ToolStripStatusLabel("جاهز"))
        statusStrip.Items.Add(New ToolStripStatusLabel($"المستخدم: {If(currentUser?.FullName, "غير محدد")}"))
        statusStrip.Items.Add(New ToolStripStatusLabel($"التاريخ: {DateTime.Now:yyyy/MM/dd}"))
        Me.Controls.Add(statusStrip)

        Me.ResumeLayout(False)
        Me.PerformLayout()
    End Sub

    ''' <summary>
    ''' تهيئة دعم اللغة العربية
    ''' </summary>
    Private Sub InitializeArabicSupport()
        ' تعيين الثقافة العربية
        Dim arabicCulture As New CultureInfo("ar-SA")
        Thread.CurrentThread.CurrentCulture = arabicCulture
        Thread.CurrentThread.CurrentUICulture = arabicCulture

        ' تعيين اتجاه النص
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
    End Sub

    ''' <summary>
    ''' تهيئة شريط التنقل
    ''' </summary>
    Private Sub InitializeNavigation()
        navBarControl = New NavBarControl()
        navBarControl.Dock = DockStyle.Right
        navBarControl.Width = 250
        navBarControl.RightToLeft = RightToLeft.Yes

        ' إنشاء مجموعات التنقل
        CreateUserManagementGroup()
        CreateSettingsGroup()
        CreateAccountingGroup()
        CreatePayrollGroup()
        CreateReportsGroup()

        Me.Controls.Add(navBarControl)
    End Sub

    ''' <summary>
    ''' تهيئة MDI
    ''' </summary>
    Private Sub InitializeMDI()
        tabbedMdiManager = New XtraTabbedMdiManager()
        tabbedMdiManager.MdiParent = Me
        tabbedMdiManager.ClosePageButtonShowMode = ClosePageButtonShowMode.InAllTabPageHeaders
    End Sub

    ''' <summary>
    ''' إنشاء مجموعة إدارة المستخدمين
    ''' </summary>
    Private Sub CreateUserManagementGroup()
        Dim userGroup As NavBarGroup = navBarControl.Groups.Add()
        userGroup.Caption = "إدارة المستخدمين"
        userGroup.Expanded = True

        ' إضافة عناصر المجموعة
        Dim usersItem As NavBarItem = userGroup.ItemLinks.Add().Item
        usersItem.Caption = "المستخدمون"
        usersItem.Tag = "Users"

        Dim userGroupsItem As NavBarItem = userGroup.ItemLinks.Add().Item
        userGroupsItem.Caption = "مجموعات المستخدمين"
        userGroupsItem.Tag = "UserGroups"

        Dim permissionsItem As NavBarItem = userGroup.ItemLinks.Add().Item
        permissionsItem.Caption = "الصلاحيات"
        permissionsItem.Tag = "Permissions"
    End Sub

    ''' <summary>
    ''' إنشاء مجموعة الإعدادات
    ''' </summary>
    Private Sub CreateSettingsGroup()
        Dim settingsGroup As NavBarGroup = navBarControl.Groups.Add()
        settingsGroup.Caption = "الإعدادات والتهيئة"
        settingsGroup.Expanded = False

        ' البيانات الأساسية
        Dim orgInfoItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        orgInfoItem.Caption = "معلومات المؤسسة"
        orgInfoItem.Tag = "OrganizationInfo"

        Dim departmentsItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        departmentsItem.Caption = "الدوائر"
        departmentsItem.Tag = "Departments"

        Dim sectionsItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        sectionsItem.Caption = "الأقسام"
        sectionsItem.Tag = "Sections"

        Dim divisionsItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        divisionsItem.Caption = "الشعب"
        divisionsItem.Tag = "Divisions"

        Dim banksItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        banksItem.Caption = "المصارف"
        banksItem.Tag = "Banks"

        Dim cashBoxesItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        cashBoxesItem.Caption = "الصناديق"
        cashBoxesItem.Tag = "CashBoxes"

        Dim currenciesItem As NavBarItem = settingsGroup.ItemLinks.Add().Item
        currenciesItem.Caption = "العملات"
        currenciesItem.Tag = "Currencies"
    End Sub

    ''' <summary>
    ''' إنشاء مجموعة المحاسبة
    ''' </summary>
    Private Sub CreateAccountingGroup()
        Dim accountingGroup As NavBarGroup = navBarControl.Groups.Add()
        accountingGroup.Caption = "المحاسبة"
        accountingGroup.Expanded = False

        ' دليل الحسابات
        Dim chartOfAccountsItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        chartOfAccountsItem.Caption = "دليل الحسابات"
        chartOfAccountsItem.Tag = "ChartOfAccounts"

        ' القيود والسندات
        Dim journalEntriesItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        journalEntriesItem.Caption = "القيود اليومية"
        journalEntriesItem.Tag = "JournalEntries"

        Dim receiptVouchersItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        receiptVouchersItem.Caption = "سندات القبض"
        receiptVouchersItem.Tag = "ReceiptVouchers"

        Dim paymentVouchersItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        paymentVouchersItem.Caption = "سندات الصرف"
        paymentVouchersItem.Tag = "PaymentVouchers"

        ' التقارير المحاسبية
        Dim trialBalanceItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        trialBalanceItem.Caption = "ميزان المراجعة"
        trialBalanceItem.Tag = "TrialBalance"

        Dim balanceSheetItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        balanceSheetItem.Caption = "الميزانية العمومية"
        balanceSheetItem.Tag = "BalanceSheet"

        Dim incomeStatementItem As NavBarItem = accountingGroup.ItemLinks.Add().Item
        incomeStatementItem.Caption = "قائمة الدخل"
        incomeStatementItem.Tag = "IncomeStatement"
    End Sub

    ''' <summary>
    ''' إنشاء مجموعة الرواتب
    ''' </summary>
    Private Sub CreatePayrollGroup()
        Dim payrollGroup As NavBarGroup = navBarControl.Groups.Add()
        payrollGroup.Caption = "الرواتب"
        payrollGroup.Expanded = False

        ' البيانات الأساسية
        Dim positionsItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        positionsItem.Caption = "المناصب"
        positionsItem.Tag = "Positions"

        Dim jobTitlesItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        jobTitlesItem.Caption = "العناوين الوظيفية"
        jobTitlesItem.Tag = "JobTitles"

        Dim qualificationsItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        qualificationsItem.Caption = "الشهادات العلمية"
        qualificationsItem.Tag = "Qualifications"

        Dim jobGradesItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        jobGradesItem.Caption = "الدرجات الوظيفية"
        jobGradesItem.Tag = "JobGrades"

        ' الموظفون
        Dim employeesItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        employeesItem.Caption = "الموظفون"
        employeesItem.Tag = "Employees"

        ' معالجة الرواتب
        Dim payrollProcessingItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        payrollProcessingItem.Caption = "معالجة الرواتب"
        payrollProcessingItem.Tag = "PayrollProcessing"

        Dim payrollReportsItem As NavBarItem = payrollGroup.ItemLinks.Add().Item
        payrollReportsItem.Caption = "تقارير الرواتب"
        payrollReportsItem.Tag = "PayrollReports"
    End Sub

    ''' <summary>
    ''' إنشاء مجموعة التقارير
    ''' </summary>
    Private Sub CreateReportsGroup()
        Dim reportsGroup As NavBarGroup = navBarControl.Groups.Add()
        reportsGroup.Caption = "التقارير"
        reportsGroup.Expanded = False

        Dim financialReportsItem As NavBarItem = reportsGroup.ItemLinks.Add().Item
        financialReportsItem.Caption = "التقارير المالية"
        financialReportsItem.Tag = "FinancialReports"

        Dim payrollReportsItem As NavBarItem = reportsGroup.ItemLinks.Add().Item
        payrollReportsItem.Caption = "تقارير الرواتب"
        payrollReportsItem.Tag = "PayrollReports"

        Dim customReportsItem As NavBarItem = reportsGroup.ItemLinks.Add().Item
        customReportsItem.Caption = "التقارير المخصصة"
        customReportsItem.Tag = "CustomReports"
    End Sub

    ''' <summary>
    ''' إعداد واجهة المستخدم بناءً على الصلاحيات
    ''' </summary>
    Private Sub SetupUserInterface()
        If currentUser Is Nothing Then Return

        ' تحديث شريط العنوان
        Me.Text = $"نظام محاسبة وزارة الشباب والرياضة - {currentUser.FullName}"

        ' يمكن إضافة منطق إخفاء/إظهار العناصر بناءً على صلاحيات المستخدم
        ' TODO: تطبيق الصلاحيات على عناصر التنقل
    End Sub

    ''' <summary>
    ''' معالج النقر على عناصر التنقل
    ''' </summary>
    Private Sub navBarControl_LinkClicked(sender As Object, e As NavBarLinkEventArgs) Handles navBarControl.LinkClicked
        Try
            Dim tag As String = e.Link.Item.Tag?.ToString()
            If String.IsNullOrEmpty(tag) Then Return

            ' فتح النموذج المناسب بناءً على العنصر المحدد
            OpenForm(tag)

        Catch ex As Exception
            XtraMessageBox.Show($"حدث خطأ أثناء فتح النموذج: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' فتح النموذج المناسب
    ''' </summary>
    ''' <param name="formTag">معرف النموذج</param>
    Private Sub OpenForm(formTag As String)
        ' التحقق من وجود النموذج مفتوح مسبقاً
        For Each childForm As Form In Me.MdiChildren
            If childForm.Tag?.ToString() = formTag Then
                childForm.Activate()
                Return
            End If
        Next

        ' إنشاء النموذج الجديد
        Dim newForm As Form = CreateFormByTag(formTag)
        If newForm IsNot Nothing Then
            newForm.Tag = formTag
            newForm.MdiParent = Me
            newForm.Show()
        End If
    End Sub

    ''' <summary>
    ''' إنشاء النموذج بناءً على المعرف
    ''' </summary>
    ''' <param name="formTag">معرف النموذج</param>
    ''' <returns>النموذج المطلوب</returns>
    Private Function CreateFormByTag(formTag As String) As Form
        Select Case formTag
            Case "Users"
                ' Return New UsersForm(currentUser)
                Return New XtraForm() With {.Text = "إدارة المستخدمين", .WindowState = FormWindowState.Maximized}
            Case "UserGroups"
                Return New XtraForm() With {.Text = "مجموعات المستخدمين", .WindowState = FormWindowState.Maximized}
            Case "Permissions"
                Return New XtraForm() With {.Text = "الصلاحيات", .WindowState = FormWindowState.Maximized}
            Case "OrganizationInfo"
                Return New XtraForm() With {.Text = "معلومات المؤسسة", .WindowState = FormWindowState.Maximized}
            Case "Departments"
                Return New XtraForm() With {.Text = "الدوائر", .WindowState = FormWindowState.Maximized}
            Case "ChartOfAccounts"
                Return New XtraForm() With {.Text = "دليل الحسابات", .WindowState = FormWindowState.Maximized}
            Case "Employees"
                Return New XtraForm() With {.Text = "الموظفون", .WindowState = FormWindowState.Maximized}
            Case Else
                Return New XtraForm() With {.Text = formTag, .WindowState = FormWindowState.Maximized}
        End Select
    End Function

    ''' <summary>
    ''' معالج إغلاق النموذج
    ''' </summary>
    Private Sub MainForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        Try
            ' تنظيف الموارد
            userService?.Dispose()
            
            ' تأكيد الإغلاق
            If XtraMessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد الإغلاق", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                e.Cancel = True
            End If
        Catch ex As Exception
            ' تجاهل أخطاء الإغلاق
        End Try
    End Sub

End Class
