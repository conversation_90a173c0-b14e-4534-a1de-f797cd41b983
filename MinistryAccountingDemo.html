<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاسبة وزارة الشباب والرياضة - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .title {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: bold;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: #45a049;
        }

        .default-credentials {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .default-credentials h4 {
            color: #2196F3;
            margin-bottom: 10px;
        }

        .main-container {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .nav-menu {
            background: #34495e;
            padding: 0;
            overflow-x: auto;
        }

        .nav-menu ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu li {
            position: relative;
        }

        .nav-menu a {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            transition: background 0.3s;
            white-space: nowrap;
        }

        .nav-menu a:hover {
            background: #2c3e50;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #2c3e50;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: none;
            z-index: 1000;
        }

        .nav-menu li:hover .dropdown {
            display: block;
        }

        .content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .welcome-card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .welcome-card p {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 1000px;
            position: relative;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-bar {
            background: #ecf0f1;
            padding: 10px 20px;
            border-top: 1px solid #bdc3c7;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
        }

        /* أنماط نظام الرواتب */
        .payroll-container {
            display: none;
        }

        .payroll-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .payroll-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            gap: 5px;
        }

        .payroll-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .payroll-tab.active {
            background: #4CAF50;
            color: white;
        }

        .payroll-tab:hover {
            background: #e9ecef;
        }

        .payroll-tab.active:hover {
            background: #45a049;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .employee-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #4CAF50;
        }

        .form-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group-inline {
            display: flex;
            flex-direction: column;
        }

        .form-group-inline label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .form-group-inline input,
        .form-group-inline select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .salary-calculation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .salary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .salary-section {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }

        .salary-section h5 {
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .salary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .salary-total {
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }

        .employees-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .employees-table th,
        .employees-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .employees-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .employees-table tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .payslip {
            background: white;
            padding: 30px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .payslip-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .payslip-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .payslip-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        /* أنماط إدارة المستخدمين */
        .users-container {
            display: none;
        }

        .users-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .users-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .user-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .user-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .section-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .section-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .section-content {
            padding: 25px;
        }

        .user-form {
            margin-bottom: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-grid-full {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            font-size: 14px;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .input-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-display {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 12px;
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-input-display:hover {
            border-color: #4CAF50;
            background: #f0f8f0;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #4CAF50;
            margin-left: 10px;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .users-table th,
        .users-table td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #e1e5e9;
        }

        .users-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .users-table tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }

        .status-admin {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-user {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-edit {
            background: #2196F3;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-edit:hover {
            background: #1976D2;
        }

        .btn-delete {
            background: #f44336;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-delete:hover {
            background: #d32f2f;
        }

        .section-actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        @media (max-width: 768px) {
            .nav-menu ul {
                flex-direction: column;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 10px;
            }

            .salary-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .payroll-tabs {
                flex-direction: column;
            }

            .payslip-info,
            .payslip-details {
                grid-template-columns: 1fr;
            }

            .users-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- نموذج تسجيل الدخول -->
    <div id="loginContainer" class="login-container">
        <div class="login-form">
            <div class="logo">وش</div>
            <h2 class="title">نظام محاسبة وزارة الشباب والرياضة</h2>
            <p class="subtitle">نظام محاسبة ورواتب متطور وذكي</p>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" required>
                </div>

                <button type="submit" class="login-btn">تسجيل الدخول</button>
            </form>

            <div class="default-credentials">
                <h4>بيانات تسجيل الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
        </div>
    </div>

    <!-- النموذج الرئيسي -->
    <div id="mainContainer" class="main-container">
        <!-- رأس الصفحة -->
        <header class="header">
            <h1>نظام محاسبة وزارة الشباب والرياضة</h1>
            <div class="user-info">
                <span id="currentUser">المستخدم: admin</span>
                <span id="currentDate"></span>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </header>

        <!-- شريط التنقل -->
        <nav class="nav-menu">
            <ul>
                <li>
                    <a href="#" onclick="showModule('users-list')">إدارة المستخدمين</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('users-list')">المستخدمون</a>
                        <a href="#" onclick="showModule('user-groups')">مجموعات المستخدمين</a>
                        <a href="#" onclick="showModule('permissions')">الصلاحيات</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('organization')">الإعدادات</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('organization')">معلومات المؤسسة</a>
                        <a href="#" onclick="showModule('departments')">الدوائر</a>
                        <a href="#" onclick="showModule('sections')">الأقسام</a>
                        <a href="#" onclick="showModule('banks')">المصارف</a>
                        <a href="#" onclick="showModule('currencies')">العملات</a>
                        <a href="#" onclick="showModule('cashboxes')">الصناديق</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('chart-accounts')">المحاسبة</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('chart-accounts')">دليل الحسابات</a>
                        <a href="#" onclick="showModule('journal-entries')">القيود اليومية</a>
                        <a href="#" onclick="showModule('receipt-vouchers')">سندات القبض</a>
                        <a href="#" onclick="showModule('payment-vouchers')">سندات الصرف</a>
                        <a href="#" onclick="showModule('trial-balance')">ميزان المراجعة</a>
                        <a href="#" onclick="showModule('balance-sheet')">الميزانية العمومية</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('employees')">الرواتب</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('employees')">الموظفون</a>
                        <a href="#" onclick="showModule('positions')">المناصب</a>
                        <a href="#" onclick="showModule('payroll-processing')">معالجة الرواتب</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('financial-reports')">التقارير</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('financial-reports')">التقارير المالية</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                        <a href="#" onclick="showModule('custom-reports')">التقارير المخصصة</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showAbout()">حول البرنامج</a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <div class="welcome-card">
                <h2>مرحباً بك في نظام محاسبة وزارة الشباب والرياضة</h2>
                <p>نظام محاسبة ورواتب متطور وذكي مصمم خصيصاً لوزارة الشباب والرياضة</p>
                <p>تم تطويره باستخدام أحدث التقنيات البرمجية والمحاسبية الحديثة</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>نظام شامل لإدارة المستخدمين والصلاحيات مع تشفير آمن وصلاحيات ديناميكية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>الإعدادات والتهيئة</h3>
                    <p>إدارة الهيكل التنظيمي والمصارف والعملات مع نظام النسخ الاحتياطي التلقائي</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>النظام المحاسبي</h3>
                    <p>شجرة حسابات متعددة المستويات مع القيود اليومية والتقارير المحاسبية الشاملة</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>نظام الرواتب</h3>
                    <p>حساب تلقائي للرواتب مع جميع المخصصات والاستقطاعات وفق المعايير الحكومية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>التقارير والتحليلات</h3>
                    <p>تقارير مالية ومحاسبية شاملة مع إمكانية التصدير إلى PDF و Excel</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>دعم اللغة العربية</h3>
                    <p>واجهة RTL كاملة مع دعم الخطوط العربية والتواريخ الهجرية والميلادية</p>
                </div>
            </div>
        </main>

        <!-- شريط الحالة -->
        <div class="status-bar">
            <span>جاهز</span>
            <span id="statusUser">المستخدم: admin</span>
            <span id="statusDate"></span>
            <span id="statusTime"></span>
        </div>
    </div>

    <!-- نافذة منبثقة للوحدات -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">عنوان الوحدة</h2>
            <div id="modalContent">محتوى الوحدة</div>

            <!-- نظام إدارة المستخدمين -->
            <div id="usersSystem" class="users-container">
                <div class="users-header">
                    <h2>👥 نظام إدارة المستخدمين - وزارة الشباب والرياضة</h2>
                    <p>نظام متطور لإدارة المستخدمين والمجموعات مع أمان عالي وصلاحيات ديناميكية</p>
                </div>

                <div class="users-grid">
                    <!-- مجموعة المستخدمين -->
                    <div class="user-section">
                        <div class="section-header">
                            <span class="section-icon">👥</span>
                            <h3 class="section-title">مجموعات المستخدمين</h3>
                        </div>
                        <div class="section-content">
                            <div class="user-form">
                                <h4 style="color: #2c3e50; margin-bottom: 15px;">إضافة مجموعة جديدة</h4>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>رقم المجموعة:</label>
                                        <input type="text" id="groupId" value="GRP001" readonly style="background: #f8f9fa;">
                                    </div>
                                    <div class="input-group">
                                        <label>اسم المجموعة:</label>
                                        <input type="text" id="groupName" placeholder="مثال: المحاسبين">
                                    </div>
                                </div>

                                <div class="form-grid-full">
                                    <div class="input-group">
                                        <label>الملاحظات:</label>
                                        <textarea id="groupNotes" placeholder="وصف المجموعة والصلاحيات..."></textarea>
                                    </div>
                                </div>

                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="addUserGroup()">
                                        ➕ إضافة المجموعة
                                    </button>
                                    <button class="btn btn-secondary" onclick="clearGroupForm()">
                                        🔄 مسح النموذج
                                    </button>
                                </div>
                            </div>

                            <!-- جدول المجموعات -->
                            <table class="users-table">
                                <thead>
                                    <tr>
                                        <th>رقم المجموعة</th>
                                        <th>اسم المجموعة</th>
                                        <th>عدد المستخدمين</th>
                                        <th>الملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="groupsTableBody">
                                    <tr>
                                        <td>GRP001</td>
                                        <td>المدراء</td>
                                        <td>3</td>
                                        <td>صلاحيات كاملة للنظام</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP001')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP001')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>GRP002</td>
                                        <td>المحاسبين</td>
                                        <td>8</td>
                                        <td>صلاحيات المحاسبة والتقارير</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP002')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP002')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>GRP003</td>
                                        <td>موظفي الرواتب</td>
                                        <td>5</td>
                                        <td>صلاحيات نظام الرواتب فقط</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP003')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP003')">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- حسابات المستخدمين -->
                    <div class="user-section">
                        <div class="section-header">
                            <span class="section-icon">🔐</span>
                            <h3 class="section-title">حسابات المستخدمين</h3>
                        </div>
                        <div class="section-content">
                            <div class="user-form">
                                <h4 style="color: #2c3e50; margin-bottom: 15px;">إضافة مستخدم جديد</h4>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>رقم الحساب:</label>
                                        <input type="text" id="userId" value="USR001" readonly style="background: #f8f9fa;">
                                    </div>
                                    <div class="input-group">
                                        <label>اسم الحساب:</label>
                                        <input type="text" id="userFullName" placeholder="الاسم الكامل">
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>اسم المستخدم:</label>
                                        <input type="text" id="username" placeholder="اسم المستخدم للدخول">
                                    </div>
                                    <div class="input-group">
                                        <label>كلمة المرور:</label>
                                        <input type="password" id="userPassword" placeholder="كلمة مرور قوية">
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>نوع الحساب:</label>
                                        <select id="userType">
                                            <option value="admin">مدير - كامل الصلاحيات</option>
                                            <option value="user">مستخدم - صلاحيات محدودة</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label>اسم المجموعة:</label>
                                        <select id="userGroup">
                                            <option value="GRP001">المدراء</option>
                                            <option value="GRP002">المحاسبين</option>
                                            <option value="GRP003">موظفي الرواتب</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-grid-full">
                                    <div class="input-group">
                                        <label>صورة المستخدم:</label>
                                        <div class="file-input-wrapper">
                                            <input type="file" id="userPhoto" class="file-input" accept="image/*" onchange="previewUserPhoto(this)">
                                            <div class="file-input-display">
                                                <span>📷 اختر صورة المستخدم</span>
                                                <img id="userPhotoPreview" class="user-avatar" style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="addUser()">
                                        ➕ إضافة المستخدم
                                    </button>
                                    <button class="btn btn-secondary" onclick="clearUserForm()">
                                        🔄 مسح النموذج
                                    </button>
                                </div>
                            </div>

                            <!-- جدول المستخدمين -->
                            <table class="users-table">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>رقم الحساب</th>
                                        <th>اسم الحساب</th>
                                        <th>اسم المستخدم</th>
                                        <th>نوع الحساب</th>
                                        <th>المجموعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR001</td>
                                        <td>أحمد محمد علي</td>
                                        <td>admin</td>
                                        <td><span class="status-badge status-admin">مدير</span></td>
                                        <td>المدراء</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editUser('USR001')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteUser('USR001')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRjk4MDAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR002</td>
                                        <td>فاطمة حسن محمود</td>
                                        <td>fatima.hassan</td>
                                        <td><span class="status-badge status-user">مستخدم</span></td>
                                        <td>المحاسبين</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editUser('USR002')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteUser('USR002')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM5QzI3QjAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR003</td>
                                        <td>محمد عبدالله أحمد</td>
                                        <td>mohammed.abdullah</td>
                                        <td><span class="status-badge status-user">مستخدم</span></td>
                                        <td>موظفي الرواتب</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editUser('USR003')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteUser('USR003')">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نظام الرواتب التفاعلي -->
            <div id="payrollSystem" class="payroll-container">
                <div class="payroll-header">
                    <h2>💰 نظام الرواتب - وزارة الشباب والرياضة</h2>
                    <p>نظام متطور لإدارة رواتب الموظفين وفق المعايير الحكومية العراقية</p>
                </div>

                <div class="payroll-tabs">
                    <button class="payroll-tab active" onclick="showPayrollTab('employees')">👥 الموظفون</button>
                    <button class="payroll-tab" onclick="showPayrollTab('positions')">🏢 المناصب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('processing')">⚙️ معالجة الرواتب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('reports')">📊 التقارير</button>
                </div>

                <!-- تبويب الموظفون -->
                <div id="employeesTab" class="tab-content active">
                    <h3>إدارة الموظفين</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>البيانات الشخصية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الرقم الوظيفي:</label>
                                    <input type="text" id="empNumber" value="2024001">
                                </div>
                                <div class="form-group-inline">
                                    <label>رقم الـ IBAN:</label>
                                    <input type="text" id="empIBAN" value="***********************">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الأول:</label>
                                    <input type="text" id="firstName" value="أحمد">
                                </div>
                                <div class="form-group-inline">
                                    <label>الاسم الثاني:</label>
                                    <input type="text" id="middleName" value="محمد">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الثالث:</label>
                                    <input type="text" id="lastName" value="علي">
                                </div>
                                <div class="form-group-inline">
                                    <label>اسم الأم:</label>
                                    <input type="text" id="motherName" value="فاطمة">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4>البيانات الوظيفية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>العنوان الوظيفي:</label>
                                    <select id="jobTitle">
                                        <option>محاسب أول</option>
                                        <option>مدير مالي</option>
                                        <option>كاتب حسابات</option>
                                        <option>مراجع حسابات</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>المنصب:</label>
                                    <select id="position" onchange="calculateSalary()">
                                        <option>رئيس قسم</option>
                                        <option>مسؤول شعبة</option>
                                        <option>موظف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهادة العلمية:</label>
                                    <select id="qualification" onchange="calculateSalary()">
                                        <option>بكالوريوس محاسبة</option>
                                        <option>ماجستير إدارة أعمال</option>
                                        <option>دبلوم عالي</option>
                                        <option>دكتوراه</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدرجة الوظيفية:</label>
                                    <select id="grade" onchange="updateSteps()">
                                        <option value="1">الدرجة الأولى</option>
                                        <option value="2">الدرجة الثانية</option>
                                        <option value="3">الدرجة الثالثة</option>
                                        <option value="4">الدرجة الرابعة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>المرحلة:</label>
                                    <select id="step" onchange="calculateSalary()">
                                        <option value="1">المرحلة الأولى</option>
                                        <option value="2">المرحلة الثانية</option>
                                        <option value="3">المرحلة الثالثة</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>تاريخ التعيين:</label>
                                    <input type="date" id="hireDate" value="2020-01-15">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="salary-calculation">
                        <h4>💰 حساب الراتب التلقائي</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>الراتب الأساسي والعلاوات</h5>
                                <div class="salary-item">
                                    <span>الراتب الأساسي:</span>
                                    <span id="basicSalary">850,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>العلاوة:</span>
                                    <span id="allowanceAmount">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>فرق راتب:</span>
                                    <span id="salaryDiff">25,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>المجموع:</span>
                                    <span id="basicTotal">950,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المخصصات</h5>
                                <div class="salary-item">
                                    <span>مخصص المنصب:</span>
                                    <span id="positionAllowance">150,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الإعالة:</span>
                                    <span id="familyAllowance">50,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الأولاد:</span>
                                    <span id="childrenAllowance">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الشهادة:</span>
                                    <span id="qualificationAllowance">100,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>المخصص الجامعي:</span>
                                    <span id="universityAllowance">80,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي المخصصات:</span>
                                    <span id="totalAllowances">455,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span id="incomeTax">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين (10%):</span>
                                    <span id="employeeRetirement">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة (15%):</span>
                                    <span id="deptContribution">210,750</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span id="socialProtection">25,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span id="bankLoans">50,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span id="totalDeductions">566,750</span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.2); border-radius: 8px;">
                            <div style="font-size: 18px; margin-bottom: 10px;">
                                <strong>إجمالي الراتب: <span id="grossSalary">1,405,000</span> دينار عراقي</strong>
                            </div>
                            <div style="font-size: 20px; font-weight: bold;">
                                <strong>صافي الراتب: <span id="netSalary">838,250</span> دينار عراقي</strong>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="saveEmployee()">💾 حفظ بيانات الموظف</button>
                        <button class="btn btn-secondary" onclick="calculateSalary()">🧮 إعادة حساب الراتب</button>
                        <button class="btn btn-primary" onclick="generatePayslip()">📄 إنشاء قسيمة راتب</button>
                    </div>

                    <!-- جدول الموظفين -->
                    <h3 style="margin-top: 30px;">قائمة الموظفين</h3>
                    <table class="employees-table">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم الكامل</th>
                                <th>العنوان الوظيفي</th>
                                <th>الدرجة</th>
                                <th>صافي الراتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024001</td>
                                <td>أحمد محمد علي</td>
                                <td>محاسب أول</td>
                                <td>الدرجة الأولى</td>
                                <td>838,250</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(1)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(1)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024002</td>
                                <td>فاطمة حسن محمود</td>
                                <td>مدير مالي</td>
                                <td>الدرجة الثانية</td>
                                <td>1,250,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(2)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(2)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024003</td>
                                <td>محمد عبدالله أحمد</td>
                                <td>كاتب حسابات</td>
                                <td>الدرجة الثالثة</td>
                                <td>650,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(3)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(3)">قسيمة راتب</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- تبويب المناصب -->
                <div id="positionsTab" class="tab-content">
                    <h3>إدارة المناصب والدرجات الوظيفية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>المناصب الوظيفية</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>رمز المنصب</th>
                                        <th>اسم المنصب</th>
                                        <th>مخصص المنصب</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>P001</td>
                                        <td>رئيس قسم</td>
                                        <td>150,000</td>
                                        <td>مسؤول عن إدارة القسم</td>
                                    </tr>
                                    <tr>
                                        <td>P002</td>
                                        <td>مسؤول شعبة</td>
                                        <td>100,000</td>
                                        <td>مسؤول عن إدارة الشعبة</td>
                                    </tr>
                                    <tr>
                                        <td>P003</td>
                                        <td>موظف</td>
                                        <td>50,000</td>
                                        <td>موظف عادي</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="form-section">
                            <h4>الدرجات الوظيفية والمراحل</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>الدرجة</th>
                                        <th>الراتب الأساسي</th>
                                        <th>عدد المراحل</th>
                                        <th>مبلغ المرحلة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>الدرجة الأولى</td>
                                        <td>850,000</td>
                                        <td>15 مرحلة</td>
                                        <td>25,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثانية</td>
                                        <td>750,000</td>
                                        <td>12 مرحلة</td>
                                        <td>20,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثالثة</td>
                                        <td>650,000</td>
                                        <td>10 مراحل</td>
                                        <td>15,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الرابعة</td>
                                        <td>550,000</td>
                                        <td>8 مراحل</td>
                                        <td>12,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب معالجة الرواتب -->
                <div id="processingTab" class="tab-content">
                    <h3>معالجة الرواتب الشهرية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات المعالجة</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهر:</label>
                                    <select id="payrollMonth">
                                        <option value="12">ديسمبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="10">أكتوبر</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>السنة:</label>
                                    <select id="payrollYear">
                                        <option value="2024">2024</option>
                                        <option value="2023">2023</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="processingDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>نوع المعالجة:</label>
                                    <select id="processingType">
                                        <option>معالجة كاملة</option>
                                        <option>معالجة جزئية</option>
                                        <option>إعادة معالجة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="processPayroll()">⚙️ بدء معالجة الرواتب</button>
                        <button class="btn btn-secondary" onclick="validatePayroll()">✅ التحقق من البيانات</button>
                        <button class="btn btn-primary" onclick="approvePayroll()">✔️ اعتماد الرواتب</button>
                    </div>

                    <div class="salary-calculation">
                        <h4>📊 ملخص معالجة الرواتب - ديسمبر 2024</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>إحصائيات عامة</h5>
                                <div class="salary-item">
                                    <span>عدد الموظفين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعالجين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعتمدين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>حالة المعالجة:</span>
                                    <span style="color: #4CAF50;">مكتملة ✅</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المبالغ الإجمالية</h5>
                                <div class="salary-item">
                                    <span>إجمالي الرواتب الأساسية:</span>
                                    <span>132,600,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي المخصصات:</span>
                                    <span>70,980,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span>88,412,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>صافي الرواتب:</span>
                                    <span>115,168,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>تفاصيل الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span>21,918,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين:</span>
                                    <span>20,358,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة:</span>
                                    <span>30,537,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span>3,900,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span>11,699,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب التقارير -->
                <div id="reportsTab" class="tab-content">
                    <h3>تقارير الرواتب</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات التقرير</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>نوع التقرير:</label>
                                    <select id="reportType">
                                        <option>تقرير رواتب شهري</option>
                                        <option>تقرير رواتب حسب القسم</option>
                                        <option>تقرير المخصصات</option>
                                        <option>تقرير الاستقطاعات</option>
                                        <option>قسائم الرواتب</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الفترة:</label>
                                    <select id="reportPeriod">
                                        <option>ديسمبر 2024</option>
                                        <option>نوفمبر 2024</option>
                                        <option>أكتوبر 2024</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>تنسيق التصدير:</label>
                                    <select id="exportFormat">
                                        <option>PDF</option>
                                        <option>Excel</option>
                                        <option>Word</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="reportDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="generateReport()">📊 إنشاء التقرير</button>
                        <button class="btn btn-secondary" onclick="previewReport()">👁️ معاينة</button>
                        <button class="btn btn-primary" onclick="exportReport()">📤 تصدير</button>
                    </div>

                    <!-- معاينة التقرير -->
                    <div class="payslip">
                        <div class="payslip-header">
                            <h2>وزارة الشباب والرياضة</h2>
                            <h3>تقرير رواتب شهر ديسمبر 2024</h3>
                            <p>تاريخ التقرير: 2024/12/19</p>
                        </div>

                        <table class="employees-table">
                            <thead>
                                <tr>
                                    <th>ت</th>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم الكامل</th>
                                    <th>العنوان الوظيفي</th>
                                    <th>الراتب الأساسي</th>
                                    <th>المخصصات</th>
                                    <th>الاستقطاعات</th>
                                    <th>صافي الراتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>2024001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>محاسب أول</td>
                                    <td>850,000</td>
                                    <td>455,000</td>
                                    <td>566,750</td>
                                    <td>838,250</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>2024002</td>
                                    <td>فاطمة حسن محمود</td>
                                    <td>مدير مالي</td>
                                    <td>1,200,000</td>
                                    <td>650,000</td>
                                    <td>600,000</td>
                                    <td>1,250,000</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>2024003</td>
                                    <td>محمد عبدالله أحمد</td>
                                    <td>كاتب حسابات</td>
                                    <td>650,000</td>
                                    <td>300,000</td>
                                    <td>300,000</td>
                                    <td>650,000</td>
                                </tr>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td colspan="4">المجموع الكلي</td>
                                    <td>2,700,000</td>
                                    <td>1,405,000</td>
                                    <td>1,466,750</td>
                                    <td>2,738,250</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-SA');
            const timeStr = now.toLocaleTimeString('ar-SA');

            document.getElementById('currentDate').textContent = dateStr;
            document.getElementById('statusDate').textContent = `التاريخ: ${dateStr}`;
            document.getElementById('statusTime').textContent = `الوقت: ${timeStr}`;
        }

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin123') {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('mainContainer').style.display = 'block';
                updateDateTime();
                setInterval(updateDateTime, 1000);

                // رسالة ترحيب
                setTimeout(() => {
                    alert('تم تسجيل الدخول بنجاح! مرحباً بك في نظام محاسبة وزارة الشباب والرياضة');
                }, 500);
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('password').value = '';
            }
        }

        // عرض الوحدات
        function showModule(moduleName) {
            const modules = {
                'users-list': {
                    title: 'إدارة المستخدمين',
                    content: '',
                    isUsers: true
                },
                'user-groups': {
                    title: 'مجموعات المستخدمين',
                    content: 'هنا يتم إدارة مجموعات المستخدمين مع صلاحيات ديناميكية. الميزات:\n\n• إنشاء مجموعات مستخدمين مخصصة\n• تعيين صلاحيات للمجموعات\n• ربط المستخدمين بالمجموعات\n• وراثة الصلاحيات من المجموعة\n• إدارة هرمية للصلاحيات\n• مجموعات افتراضية: المدراء، المحاسبين، المراجعين'
                },
                'permissions': {
                    title: 'إدارة الصلاحيات',
                    content: 'هنا يتم إدارة صلاحيات النظام بشكل مفصل. النظام يدعم:\n\n• صلاحيات على مستوى الوحدات\n• أنواع الصلاحيات: قراءة، إضافة، تعديل، حذف\n• صلاحيات فردية للمستخدمين\n• صلاحيات جماعية للمجموعات\n• تسجيل تاريخ منح الصلاحيات\n• إمكانية إلغاء وتعديل الصلاحيات'
                },
                'organization': {
                    title: 'معلومات المؤسسة',
                    content: 'هنا يتم إدارة معلومات وزارة الشباب والرياضة. يشمل:\n\n• اسم المؤسسة بالعربية والإنجليزية\n• العنوان الكامل\n• أرقام الهاتف والفاكس\n• البريد الإلكتروني والموقع\n• شعار المؤسسة\n• تاريخ آخر تحديث\n• معلومات إضافية للتقارير الرسمية'
                },
                'departments': {
                    title: 'إدارة الدوائر',
                    content: 'هنا يتم إدارة دوائر الوزارة. الدوائر المتاحة:\n\n• دائرة الطب الرياضي\n• دائرة الشؤون الإدارية والمالية\n• دائرة التخطيط والمتابعة\n• دائرة الأنشطة الرياضية\n• دائرة الشباب والكشافة\n• دائرة الإعلام الرياضي\n\nكل دائرة لها رمز خاص ووصف تفصيلي'
                },
                'sections': {
                    title: 'إدارة الأقسام',
                    content: 'هنا يتم إدارة أقسام الدوائر. أمثلة على الأقسام:\n\n• القسم الإداري\n• قسم المحاسبة والمالية\n• قسم الموارد البشرية\n• قسم تقنية المعلومات\n• قسم الخدمات العامة\n• قسم المتابعة والتقييم\n\nكل قسم مرتبط بدائرة محددة'
                },
                'banks': {
                    title: 'إدارة المصارف',
                    content: 'هنا يتم إدارة المصارف وفروعها. يشمل:\n\n• المصارف الحكومية والأهلية\n• فروع المصارف في المحافظات\n• حسابات التشغيل والرواتب\n• أرقام الحسابات البنكية\n• معلومات الاتصال بالفروع\n• ربط الموظفين بالحسابات البنكية\n• إدارة التحويلات المصرفية'
                },
                'currencies': {
                    title: 'إدارة العملات',
                    content: 'هنا يتم إدارة العملات وأسعار الصرف:\n\n• الدينار العراقي (العملة الأساسية)\n• الدولار الأمريكي\n• اليورو\n• أسعار الصرف اليومية\n• تحديث أسعار الصرف\n• تحويل العملات في التقارير\n• العملة الافتراضية للنظام'
                },
                'cashboxes': {
                    title: 'إدارة الصناديق',
                    content: 'هنا يتم إدارة صناديق النقد:\n\n• صندوق النقدية الرئيسي\n• صناديق الدوائر الفرعية\n• تتبع الأرصدة الحالية\n• عمليات الإيداع والسحب\n• تسوية الصناديق اليومية\n• تقارير حركة النقد\n• ربط الصناديق بالمستخدمين'
                },
                'chart-accounts': {
                    title: 'دليل الحسابات',
                    content: 'هنا يتم إدارة شجرة الحسابات المحاسبية وفق النظام الحكومي:\n\n• نوع الاستمارة (تشغيلية/استثمارية)\n• نوع النفقة (جارية/رأسمالية)\n• الفصل والمادة والنوع\n• حسابات تحليلية وإجمالية\n• الأرصدة الافتتاحية والحالية\n• ربط الحسابات الختامية\n• هيكل هرمي متعدد المستويات'
                },
                'journal-entries': {
                    title: 'القيود اليومية',
                    content: 'هنا يتم إدخال وإدارة القيود المحاسبية:\n\n• قيود يومية عادية\n• قيود التسوية\n• قيود الإقفال\n• قيود الافتتاح\n• نظام الموافقات المتدرج\n• ترقيم تلقائي للقيود\n• ربط القيود بالمستندات\n• تدقيق وتوازن القيود'
                },
                'receipt-vouchers': {
                    title: 'سندات القبض',
                    content: 'هنا يتم إدارة سندات القبض:\n\n• سندات قبض نقدية\n• سندات قبض بنكية\n• ربط بالحسابات البنكية\n• ترقيم تلقائي للسندات\n• طباعة السندات\n• تتبع حالة السندات\n• ربط بالقيود المحاسبية\n• تقارير سندات القبض'
                },
                'payment-vouchers': {
                    title: 'سندات الصرف',
                    content: 'هنا يتم إدارة سندات الصرف:\n\n• سندات صرف نقدية\n• سندات صرف بنكية\n• ربط بالحسابات البنكية\n• نظام الموافقات\n• طباعة السندات\n• تتبع المدفوعات\n• ربط بالقيود المحاسبية\n• تقارير سندات الصرف'
                },
                'trial-balance': {
                    title: 'ميزان المراجعة',
                    content: 'هنا يتم عرض ميزان المراجعة:\n\n• ميزان المراجعة الشهري\n• ميزان المراجعة السنوي\n• الأرصدة الافتتاحية\n• حركة الحسابات (مدين/دائن)\n• الأرصدة الختامية\n• التحقق من توازن الميزان\n• تصدير إلى Excel و PDF\n• ميزان مقارن بين الفترات'
                },
                'balance-sheet': {
                    title: 'الميزانية العمومية',
                    content: 'هنا يتم عرض الميزانية العمومية:\n\n• الأصول الثابتة والمتداولة\n• الخصوم والالتزامات\n• حقوق الملكية\n• الميزانية الشهرية والسنوية\n• مقارنة بين السنوات\n• تحليل المؤشرات المالية\n• تصدير التقارير\n• الميزانية المبسطة والمفصلة'
                },
                'employees': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'positions': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'payroll-processing': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'financial-reports': {
                    title: 'التقارير المالية',
                    content: 'هنا يتم عرض التقارير المالية الشاملة:\n\n• تقرير المركز المالي\n• تقرير الدخل والمصروفات\n• تقرير التدفقات النقدية\n• تقرير الأداء المالي\n• تقارير الموازنة والتنفيذ\n• تقارير مقارنة بين الفترات\n• تحليل الانحرافات\n• مؤشرات الأداء المالي\n• تصدير بصيغ متعددة'
                },
                'payroll-reports': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'custom-reports': {
                    title: 'التقارير المخصصة',
                    content: 'هنا يتم إنشاء تقارير مخصصة حسب الحاجة:\n\n• منشئ التقارير التفاعلي\n• اختيار الحقول والمعايير\n• تصفية البيانات المتقدمة\n• تجميع وتلخيص البيانات\n• رسوم بيانية وإحصائيات\n• جدولة التقارير التلقائية\n• مشاركة التقارير\n• حفظ قوالب التقارير\n• تصدير بصيغ متعددة'
                }
            };

            const module = modules[moduleName];
            if (module) {
                document.getElementById('modalTitle').textContent = module.title;

                if (module.isUsers) {
                    // إخفاء المحتوى العادي وإظهار نظام إدارة المستخدمين
                    document.getElementById('modalContent').style.display = 'none';
                    document.getElementById('payrollSystem').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'block';
                } else if (module.isPayroll) {
                    // إخفاء المحتوى العادي وإظهار نظام الرواتب
                    document.getElementById('modalContent').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'none';
                    document.getElementById('payrollSystem').style.display = 'block';

                    // تحديد التبويب المناسب
                    if (moduleName === 'employees') {
                        showPayrollTab('employees');
                    } else if (moduleName === 'positions') {
                        showPayrollTab('positions');
                    } else if (moduleName === 'payroll-processing') {
                        showPayrollTab('processing');
                    } else if (moduleName === 'payroll-reports') {
                        showPayrollTab('reports');
                    }
                } else {
                    // إظهار المحتوى العادي وإخفاء الأنظمة الأخرى
                    document.getElementById('modalContent').style.display = 'block';
                    document.getElementById('payrollSystem').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'none';

                    // تحويل النص إلى HTML مع تنسيق أفضل
                    const formattedContent = module.content
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/\n/g, '<br>')
                        .replace(/•/g, '<span style="color: #4CAF50; font-weight: bold;">•</span>');

                    document.getElementById('modalContent').innerHTML = `
                        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <p style="line-height: 1.8; font-size: 16px; color: #333; margin: 0;">${formattedContent}</p>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="alert('هذه الوحدة متاحة في النسخة الكاملة من النظام')">
                                🚀 تفعيل الوحدة
                            </button>
                        </div>
                    `;
                }

                document.getElementById('moduleModal').style.display = 'block';
            }
        }

        // وظائف نظام الرواتب
        function showPayrollTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            const tabButtons = document.querySelectorAll('.payroll-tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');

            // تفعيل الزر المناسب
            event.target.classList.add('active');
        }

        function updateSteps() {
            const grade = document.getElementById('grade').value;
            const stepSelect = document.getElementById('step');

            // مسح الخيارات الحالية
            stepSelect.innerHTML = '';

            // إضافة المراحل حسب الدرجة
            const steps = {
                '1': 15, // الدرجة الأولى - 15 مرحلة
                '2': 12, // الدرجة الثانية - 12 مرحلة
                '3': 10, // الدرجة الثالثة - 10 مراحل
                '4': 8   // الدرجة الرابعة - 8 مراحل
            };

            const stepCount = steps[grade] || 10;
            for (let i = 1; i <= stepCount; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `المرحلة ${i}`;
                stepSelect.appendChild(option);
            }

            // إعادة حساب الراتب
            calculateSalary();
        }

        function calculateSalary() {
            // الحصول على القيم
            const grade = parseInt(document.getElementById('grade').value);
            const step = parseInt(document.getElementById('step').value) || 1;
            const position = document.getElementById('position').value;
            const qualification = document.getElementById('qualification').value;

            // الرواتب الأساسية حسب الدرجة
            const basicSalaries = {
                1: 850000,
                2: 750000,
                3: 650000,
                4: 550000
            };

            // مبالغ المراحل
            const stepAmounts = {
                1: 25000,
                2: 20000,
                3: 15000,
                4: 12000
            };

            // حساب الراتب الأساسي
            const basicSalary = basicSalaries[grade] || 650000;
            const allowance = 75000; // علاوة ثابتة
            const salaryDiff = (step - 1) * (stepAmounts[grade] || 15000);
            const basicTotal = basicSalary + allowance + salaryDiff;

            // حساب المخصصات
            const positionAllowances = {
                'رئيس قسم': 150000,
                'مسؤول شعبة': 100000,
                'موظف': 50000
            };

            const qualificationAllowances = {
                'دكتوراه': 150000,
                'ماجستير إدارة أعمال': 120000,
                'بكالوريوس محاسبة': 100000,
                'دبلوم عالي': 80000
            };

            const positionAllowance = positionAllowances[position] || 50000;
            const familyAllowance = 50000;
            const childrenAllowance = 75000;
            const qualificationAllowance = qualificationAllowances[qualification] || 100000;
            const universityAllowance = 80000;

            const totalAllowances = positionAllowance + familyAllowance + childrenAllowance +
                                  qualificationAllowance + universityAllowance;

            // حساب إجمالي الراتب
            const grossSalary = basicTotal + totalAllowances;

            // حساب الاستقطاعات
            const incomeTax = grossSalary * 0.10; // 10% ضريبة دخل
            const employeeRetirement = grossSalary * 0.10; // 10% تقاعد موظفين
            const deptContribution = grossSalary * 0.15; // 15% مساهمة دائرة
            const socialProtection = 25000;
            const bankLoans = 50000;

            const totalDeductions = incomeTax + employeeRetirement + deptContribution +
                                  socialProtection + bankLoans;

            // حساب صافي الراتب
            const netSalary = grossSalary - totalDeductions;

            // تحديث العرض
            document.getElementById('basicSalary').textContent = basicSalary.toLocaleString();
            document.getElementById('allowanceAmount').textContent = allowance.toLocaleString();
            document.getElementById('salaryDiff').textContent = salaryDiff.toLocaleString();
            document.getElementById('basicTotal').textContent = basicTotal.toLocaleString();

            document.getElementById('positionAllowance').textContent = positionAllowance.toLocaleString();
            document.getElementById('familyAllowance').textContent = familyAllowance.toLocaleString();
            document.getElementById('childrenAllowance').textContent = childrenAllowance.toLocaleString();
            document.getElementById('qualificationAllowance').textContent = qualificationAllowance.toLocaleString();
            document.getElementById('universityAllowance').textContent = universityAllowance.toLocaleString();
            document.getElementById('totalAllowances').textContent = totalAllowances.toLocaleString();

            document.getElementById('incomeTax').textContent = Math.round(incomeTax).toLocaleString();
            document.getElementById('employeeRetirement').textContent = Math.round(employeeRetirement).toLocaleString();
            document.getElementById('deptContribution').textContent = Math.round(deptContribution).toLocaleString();
            document.getElementById('socialProtection').textContent = socialProtection.toLocaleString();
            document.getElementById('bankLoans').textContent = bankLoans.toLocaleString();
            document.getElementById('totalDeductions').textContent = Math.round(totalDeductions).toLocaleString();

            document.getElementById('grossSalary').textContent = grossSalary.toLocaleString();
            document.getElementById('netSalary').textContent = Math.round(netSalary).toLocaleString();
        }

        function saveEmployee() {
            const firstName = document.getElementById('firstName').value;
            const middleName = document.getElementById('middleName').value;
            const lastName = document.getElementById('lastName').value;

            alert(`تم حفظ بيانات الموظف: ${firstName} ${middleName} ${lastName} بنجاح!`);
        }

        function generatePayslip() {
            alert('تم إنشاء قسيمة الراتب بنجاح! سيتم تحميل الملف قريباً...');
        }

        function editEmployee(id) {
            alert(`تحرير بيانات الموظف رقم: ${id}`);
        }

        function viewPayslip(id) {
            alert(`عرض قسيمة راتب الموظف رقم: ${id}`);
        }

        function processPayroll() {
            const month = document.getElementById('payrollMonth').value;
            const year = document.getElementById('payrollYear').value;

            alert(`تم بدء معالجة رواتب شهر ${month}/${year} بنجاح!`);
        }

        function validatePayroll() {
            alert('تم التحقق من صحة البيانات بنجاح! جميع البيانات صحيحة ✅');
        }

        function approvePayroll() {
            alert('تم اعتماد الرواتب بنجاح! ✅');
        }

        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            alert(`تم إنشاء ${reportType} بنجاح!`);
        }

        function previewReport() {
            alert('معاينة التقرير متاحة أدناه 👁️');
        }

        function exportReport() {
            const format = document.getElementById('exportFormat').value;
            alert(`تم تصدير التقرير بصيغة ${format} بنجاح! 📤`);
        }

        // وظائف نظام إدارة المستخدمين
        let groupCounter = 4;
        let userCounter = 4;

        function addUserGroup() {
            const groupName = document.getElementById('groupName').value;
            const groupNotes = document.getElementById('groupNotes').value;

            if (!groupName.trim()) {
                alert('يرجى إدخال اسم المجموعة');
                return;
            }

            const newGroupId = `GRP${String(groupCounter).padStart(3, '0')}`;
            const tableBody = document.getElementById('groupsTableBody');

            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${newGroupId}</td>
                <td>${groupName}</td>
                <td>0</td>
                <td>${groupNotes || 'لا توجد ملاحظات'}</td>
                <td class="action-buttons">
                    <button class="btn-edit btn-small" onclick="editGroup('${newGroupId}')">تعديل</button>
                    <button class="btn-delete btn-small" onclick="deleteGroup('${newGroupId}')">حذف</button>
                </td>
            `;

            tableBody.appendChild(newRow);

            // تحديث قائمة المجموعات في نموذج المستخدمين
            const userGroupSelect = document.getElementById('userGroup');
            const newOption = document.createElement('option');
            newOption.value = newGroupId;
            newOption.textContent = groupName;
            userGroupSelect.appendChild(newOption);

            groupCounter++;
            document.getElementById('groupId').value = `GRP${String(groupCounter).padStart(3, '0')}`;

            clearGroupForm();
            alert(`تم إضافة المجموعة "${groupName}" بنجاح! ✅`);
        }

        function clearGroupForm() {
            document.getElementById('groupName').value = '';
            document.getElementById('groupNotes').value = '';
        }

        function editGroup(groupId) {
            alert(`تحرير المجموعة: ${groupId}`);
        }

        function deleteGroup(groupId) {
            if (confirm(`هل تريد حذف المجموعة ${groupId}؟`)) {
                // حذف الصف من الجدول
                const rows = document.querySelectorAll('#groupsTableBody tr');
                rows.forEach(row => {
                    if (row.cells[0].textContent === groupId) {
                        row.remove();
                    }
                });

                // حذف من قائمة المجموعات في نموذج المستخدمين
                const userGroupSelect = document.getElementById('userGroup');
                const options = userGroupSelect.querySelectorAll('option');
                options.forEach(option => {
                    if (option.value === groupId) {
                        option.remove();
                    }
                });

                alert(`تم حذف المجموعة ${groupId} بنجاح! ✅`);
            }
        }

        function addUser() {
            const userFullName = document.getElementById('userFullName').value;
            const username = document.getElementById('username').value;
            const userPassword = document.getElementById('userPassword').value;
            const userType = document.getElementById('userType').value;
            const userGroup = document.getElementById('userGroup').value;

            if (!userFullName.trim() || !username.trim() || !userPassword.trim()) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const newUserId = `USR${String(userCounter).padStart(3, '0')}`;
            const tableBody = document.getElementById('usersTableBody');

            // إنشاء صورة افتراضية
            const avatarColors = ['#4CAF50', '#FF9800', '#9C27B0', '#2196F3', '#F44336'];
            const randomColor = avatarColors[Math.floor(Math.random() * avatarColors.length)];
            const avatarSvg = `data:image/svg+xml;base64,${btoa(`
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="20" fill="${randomColor}"/>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z" fill="white"/>
                        <path d="M12 14C8.68629 14 6 16.6863 6 20V22H18V20C18 16.6863 15.3137 14 12 14Z" fill="white"/>
                    </svg>
                </svg>
            `)}`;

            const userTypeText = userType === 'admin' ? 'مدير' : 'مستخدم';
            const statusClass = userType === 'admin' ? 'status-admin' : 'status-user';

            // الحصول على اسم المجموعة
            const groupSelect = document.getElementById('userGroup');
            const groupName = groupSelect.options[groupSelect.selectedIndex].text;

            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>
                    <img src="${avatarSvg}" class="user-avatar" alt="صورة المستخدم">
                </td>
                <td>${newUserId}</td>
                <td>${userFullName}</td>
                <td>${username}</td>
                <td><span class="status-badge ${statusClass}">${userTypeText}</span></td>
                <td>${groupName}</td>
                <td class="action-buttons">
                    <button class="btn-edit btn-small" onclick="editUser('${newUserId}')">تعديل</button>
                    <button class="btn-delete btn-small" onclick="deleteUser('${newUserId}')">حذف</button>
                </td>
            `;

            tableBody.appendChild(newRow);

            userCounter++;
            document.getElementById('userId').value = `USR${String(userCounter).padStart(3, '0')}`;

            clearUserForm();
            alert(`تم إضافة المستخدم "${userFullName}" بنجاح! ✅`);
        }

        function clearUserForm() {
            document.getElementById('userFullName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('userPassword').value = '';
            document.getElementById('userType').selectedIndex = 0;
            document.getElementById('userGroup').selectedIndex = 0;

            // إخفاء معاينة الصورة
            const preview = document.getElementById('userPhotoPreview');
            preview.style.display = 'none';
            document.querySelector('.file-input-display span').textContent = '📷 اختر صورة المستخدم';
        }

        function editUser(userId) {
            alert(`تحرير المستخدم: ${userId}`);
        }

        function deleteUser(userId) {
            if (confirm(`هل تريد حذف المستخدم ${userId}؟`)) {
                const rows = document.querySelectorAll('#usersTableBody tr');
                rows.forEach(row => {
                    if (row.cells[1].textContent === userId) {
                        row.remove();
                    }
                });
                alert(`تم حذف المستخدم ${userId} بنجاح! ✅`);
            }
        }

        function previewUserPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('userPhotoPreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    document.querySelector('.file-input-display span').textContent = '✅ تم اختيار الصورة';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('moduleModal').style.display = 'none';
        }

        // عرض معلومات البرنامج
        function showAbout() {
            alert('نظام محاسبة وزارة الشباب والرياضة\nالإصدار 1.0.0\nتم التطوير باستخدام VB.NET\nجميع الحقوق محفوظة © 2024');
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('moduleModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // تحديث التاريخ والوقت عند تحميل الصفحة
        updateDateTime();

        // تحديث حسابات الراتب عند تحميل الصفحة
        setTimeout(() => {
            if (document.getElementById('basicSalary')) {
                calculateSalary();
            }
        }, 1000);
    </script>
</body>
</html>
