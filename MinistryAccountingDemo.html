<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاسبة وزارة الشباب والرياضة - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .title {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: bold;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: #45a049;
        }

        .default-credentials {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .default-credentials h4 {
            color: #2196F3;
            margin-bottom: 10px;
        }

        .main-container {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .nav-menu {
            background: #34495e;
            padding: 0;
            overflow-x: auto;
        }

        .nav-menu ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu li {
            position: relative;
        }

        .nav-menu a {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            transition: background 0.3s;
            white-space: nowrap;
        }

        .nav-menu a:hover {
            background: #2c3e50;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #2c3e50;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: none;
            z-index: 1000;
        }

        .nav-menu li:hover .dropdown {
            display: block;
        }

        .content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .welcome-card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .welcome-card p {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 1000px;
            position: relative;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-bar {
            background: #ecf0f1;
            padding: 10px 20px;
            border-top: 1px solid #bdc3c7;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
        }

        /* أنماط نظام الرواتب */
        .payroll-container {
            display: none;
        }

        .payroll-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .payroll-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            gap: 5px;
        }

        .payroll-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .payroll-tab.active {
            background: #4CAF50;
            color: white;
        }

        .payroll-tab:hover {
            background: #e9ecef;
        }

        .payroll-tab.active:hover {
            background: #45a049;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .employee-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #4CAF50;
        }

        .form-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group-inline {
            display: flex;
            flex-direction: column;
        }

        .form-group-inline label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .form-group-inline input,
        .form-group-inline select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .salary-calculation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .salary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .salary-section {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }

        .salary-section h5 {
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .salary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .salary-total {
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }

        .employees-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .employees-table th,
        .employees-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .employees-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .employees-table tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .payslip {
            background: white;
            padding: 30px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .payslip-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .payslip-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .payslip-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .nav-menu ul {
                flex-direction: column;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 10px;
            }

            .salary-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .payroll-tabs {
                flex-direction: column;
            }

            .payslip-info,
            .payslip-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- نموذج تسجيل الدخول -->
    <div id="loginContainer" class="login-container">
        <div class="login-form">
            <div class="logo">وش</div>
            <h2 class="title">نظام محاسبة وزارة الشباب والرياضة</h2>
            <p class="subtitle">نظام محاسبة ورواتب متطور وذكي</p>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" required>
                </div>

                <button type="submit" class="login-btn">تسجيل الدخول</button>
            </form>

            <div class="default-credentials">
                <h4>بيانات تسجيل الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
        </div>
    </div>

    <!-- النموذج الرئيسي -->
    <div id="mainContainer" class="main-container">
        <!-- رأس الصفحة -->
        <header class="header">
            <h1>نظام محاسبة وزارة الشباب والرياضة</h1>
            <div class="user-info">
                <span id="currentUser">المستخدم: admin</span>
                <span id="currentDate"></span>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </header>

        <!-- شريط التنقل -->
        <nav class="nav-menu">
            <ul>
                <li>
                    <a href="#" onclick="showModule('users')">إدارة المستخدمين</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('users-list')">المستخدمون</a>
                        <a href="#" onclick="showModule('user-groups')">مجموعات المستخدمين</a>
                        <a href="#" onclick="showModule('permissions')">الصلاحيات</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('settings')">الإعدادات</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('organization')">معلومات المؤسسة</a>
                        <a href="#" onclick="showModule('departments')">الدوائر</a>
                        <a href="#" onclick="showModule('sections')">الأقسام</a>
                        <a href="#" onclick="showModule('banks')">المصارف</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('accounting')">المحاسبة</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('chart-accounts')">دليل الحسابات</a>
                        <a href="#" onclick="showModule('journal-entries')">القيود اليومية</a>
                        <a href="#" onclick="showModule('receipt-vouchers')">سندات القبض</a>
                        <a href="#" onclick="showModule('payment-vouchers')">سندات الصرف</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('payroll')">الرواتب</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('employees')">الموظفون</a>
                        <a href="#" onclick="showModule('positions')">المناصب</a>
                        <a href="#" onclick="showModule('payroll-processing')">معالجة الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('reports')">التقارير</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('financial-reports')">التقارير المالية</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showAbout()">حول البرنامج</a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <div class="welcome-card">
                <h2>مرحباً بك في نظام محاسبة وزارة الشباب والرياضة</h2>
                <p>نظام محاسبة ورواتب متطور وذكي مصمم خصيصاً لوزارة الشباب والرياضة</p>
                <p>تم تطويره باستخدام أحدث التقنيات البرمجية والمحاسبية الحديثة</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>نظام شامل لإدارة المستخدمين والصلاحيات مع تشفير آمن وصلاحيات ديناميكية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>الإعدادات والتهيئة</h3>
                    <p>إدارة الهيكل التنظيمي والمصارف والعملات مع نظام النسخ الاحتياطي التلقائي</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>النظام المحاسبي</h3>
                    <p>شجرة حسابات متعددة المستويات مع القيود اليومية والتقارير المحاسبية الشاملة</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>نظام الرواتب</h3>
                    <p>حساب تلقائي للرواتب مع جميع المخصصات والاستقطاعات وفق المعايير الحكومية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>التقارير والتحليلات</h3>
                    <p>تقارير مالية ومحاسبية شاملة مع إمكانية التصدير إلى PDF و Excel</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>دعم اللغة العربية</h3>
                    <p>واجهة RTL كاملة مع دعم الخطوط العربية والتواريخ الهجرية والميلادية</p>
                </div>
            </div>
        </main>

        <!-- شريط الحالة -->
        <div class="status-bar">
            <span>جاهز</span>
            <span id="statusUser">المستخدم: admin</span>
            <span id="statusDate"></span>
            <span id="statusTime"></span>
        </div>
    </div>

    <!-- نافذة منبثقة للوحدات -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">عنوان الوحدة</h2>
            <div id="modalContent">محتوى الوحدة</div>

            <!-- نظام الرواتب التفاعلي -->
            <div id="payrollSystem" class="payroll-container">
                <div class="payroll-header">
                    <h2>💰 نظام الرواتب - وزارة الشباب والرياضة</h2>
                    <p>نظام متطور لإدارة رواتب الموظفين وفق المعايير الحكومية العراقية</p>
                </div>

                <div class="payroll-tabs">
                    <button class="payroll-tab active" onclick="showPayrollTab('employees')">👥 الموظفون</button>
                    <button class="payroll-tab" onclick="showPayrollTab('positions')">🏢 المناصب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('processing')">⚙️ معالجة الرواتب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('reports')">📊 التقارير</button>
                </div>

                <!-- تبويب الموظفون -->
                <div id="employeesTab" class="tab-content active">
                    <h3>إدارة الموظفين</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>البيانات الشخصية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الرقم الوظيفي:</label>
                                    <input type="text" id="empNumber" value="2024001">
                                </div>
                                <div class="form-group-inline">
                                    <label>رقم الـ IBAN:</label>
                                    <input type="text" id="empIBAN" value="***********************">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الأول:</label>
                                    <input type="text" id="firstName" value="أحمد">
                                </div>
                                <div class="form-group-inline">
                                    <label>الاسم الثاني:</label>
                                    <input type="text" id="middleName" value="محمد">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الثالث:</label>
                                    <input type="text" id="lastName" value="علي">
                                </div>
                                <div class="form-group-inline">
                                    <label>اسم الأم:</label>
                                    <input type="text" id="motherName" value="فاطمة">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4>البيانات الوظيفية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>العنوان الوظيفي:</label>
                                    <select id="jobTitle">
                                        <option>محاسب أول</option>
                                        <option>مدير مالي</option>
                                        <option>كاتب حسابات</option>
                                        <option>مراجع حسابات</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>المنصب:</label>
                                    <select id="position" onchange="calculateSalary()">
                                        <option>رئيس قسم</option>
                                        <option>مسؤول شعبة</option>
                                        <option>موظف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهادة العلمية:</label>
                                    <select id="qualification" onchange="calculateSalary()">
                                        <option>بكالوريوس محاسبة</option>
                                        <option>ماجستير إدارة أعمال</option>
                                        <option>دبلوم عالي</option>
                                        <option>دكتوراه</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدرجة الوظيفية:</label>
                                    <select id="grade" onchange="updateSteps()">
                                        <option value="1">الدرجة الأولى</option>
                                        <option value="2">الدرجة الثانية</option>
                                        <option value="3">الدرجة الثالثة</option>
                                        <option value="4">الدرجة الرابعة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>المرحلة:</label>
                                    <select id="step" onchange="calculateSalary()">
                                        <option value="1">المرحلة الأولى</option>
                                        <option value="2">المرحلة الثانية</option>
                                        <option value="3">المرحلة الثالثة</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>تاريخ التعيين:</label>
                                    <input type="date" id="hireDate" value="2020-01-15">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="salary-calculation">
                        <h4>💰 حساب الراتب التلقائي</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>الراتب الأساسي والعلاوات</h5>
                                <div class="salary-item">
                                    <span>الراتب الأساسي:</span>
                                    <span id="basicSalary">850,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>العلاوة:</span>
                                    <span id="allowanceAmount">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>فرق راتب:</span>
                                    <span id="salaryDiff">25,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>المجموع:</span>
                                    <span id="basicTotal">950,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المخصصات</h5>
                                <div class="salary-item">
                                    <span>مخصص المنصب:</span>
                                    <span id="positionAllowance">150,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الإعالة:</span>
                                    <span id="familyAllowance">50,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الأولاد:</span>
                                    <span id="childrenAllowance">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الشهادة:</span>
                                    <span id="qualificationAllowance">100,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>المخصص الجامعي:</span>
                                    <span id="universityAllowance">80,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي المخصصات:</span>
                                    <span id="totalAllowances">455,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span id="incomeTax">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين (10%):</span>
                                    <span id="employeeRetirement">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة (15%):</span>
                                    <span id="deptContribution">210,750</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span id="socialProtection">25,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span id="bankLoans">50,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span id="totalDeductions">566,750</span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.2); border-radius: 8px;">
                            <div style="font-size: 18px; margin-bottom: 10px;">
                                <strong>إجمالي الراتب: <span id="grossSalary">1,405,000</span> دينار عراقي</strong>
                            </div>
                            <div style="font-size: 20px; font-weight: bold;">
                                <strong>صافي الراتب: <span id="netSalary">838,250</span> دينار عراقي</strong>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="saveEmployee()">💾 حفظ بيانات الموظف</button>
                        <button class="btn btn-secondary" onclick="calculateSalary()">🧮 إعادة حساب الراتب</button>
                        <button class="btn btn-primary" onclick="generatePayslip()">📄 إنشاء قسيمة راتب</button>
                    </div>

                    <!-- جدول الموظفين -->
                    <h3 style="margin-top: 30px;">قائمة الموظفين</h3>
                    <table class="employees-table">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم الكامل</th>
                                <th>العنوان الوظيفي</th>
                                <th>الدرجة</th>
                                <th>صافي الراتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024001</td>
                                <td>أحمد محمد علي</td>
                                <td>محاسب أول</td>
                                <td>الدرجة الأولى</td>
                                <td>838,250</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(1)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(1)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024002</td>
                                <td>فاطمة حسن محمود</td>
                                <td>مدير مالي</td>
                                <td>الدرجة الثانية</td>
                                <td>1,250,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(2)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(2)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024003</td>
                                <td>محمد عبدالله أحمد</td>
                                <td>كاتب حسابات</td>
                                <td>الدرجة الثالثة</td>
                                <td>650,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(3)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(3)">قسيمة راتب</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- تبويب المناصب -->
                <div id="positionsTab" class="tab-content">
                    <h3>إدارة المناصب والدرجات الوظيفية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>المناصب الوظيفية</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>رمز المنصب</th>
                                        <th>اسم المنصب</th>
                                        <th>مخصص المنصب</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>P001</td>
                                        <td>رئيس قسم</td>
                                        <td>150,000</td>
                                        <td>مسؤول عن إدارة القسم</td>
                                    </tr>
                                    <tr>
                                        <td>P002</td>
                                        <td>مسؤول شعبة</td>
                                        <td>100,000</td>
                                        <td>مسؤول عن إدارة الشعبة</td>
                                    </tr>
                                    <tr>
                                        <td>P003</td>
                                        <td>موظف</td>
                                        <td>50,000</td>
                                        <td>موظف عادي</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="form-section">
                            <h4>الدرجات الوظيفية والمراحل</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>الدرجة</th>
                                        <th>الراتب الأساسي</th>
                                        <th>عدد المراحل</th>
                                        <th>مبلغ المرحلة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>الدرجة الأولى</td>
                                        <td>850,000</td>
                                        <td>15 مرحلة</td>
                                        <td>25,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثانية</td>
                                        <td>750,000</td>
                                        <td>12 مرحلة</td>
                                        <td>20,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثالثة</td>
                                        <td>650,000</td>
                                        <td>10 مراحل</td>
                                        <td>15,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الرابعة</td>
                                        <td>550,000</td>
                                        <td>8 مراحل</td>
                                        <td>12,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب معالجة الرواتب -->
                <div id="processingTab" class="tab-content">
                    <h3>معالجة الرواتب الشهرية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات المعالجة</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهر:</label>
                                    <select id="payrollMonth">
                                        <option value="12">ديسمبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="10">أكتوبر</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>السنة:</label>
                                    <select id="payrollYear">
                                        <option value="2024">2024</option>
                                        <option value="2023">2023</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="processingDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>نوع المعالجة:</label>
                                    <select id="processingType">
                                        <option>معالجة كاملة</option>
                                        <option>معالجة جزئية</option>
                                        <option>إعادة معالجة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="processPayroll()">⚙️ بدء معالجة الرواتب</button>
                        <button class="btn btn-secondary" onclick="validatePayroll()">✅ التحقق من البيانات</button>
                        <button class="btn btn-primary" onclick="approvePayroll()">✔️ اعتماد الرواتب</button>
                    </div>

                    <div class="salary-calculation">
                        <h4>📊 ملخص معالجة الرواتب - ديسمبر 2024</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>إحصائيات عامة</h5>
                                <div class="salary-item">
                                    <span>عدد الموظفين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعالجين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعتمدين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>حالة المعالجة:</span>
                                    <span style="color: #4CAF50;">مكتملة ✅</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المبالغ الإجمالية</h5>
                                <div class="salary-item">
                                    <span>إجمالي الرواتب الأساسية:</span>
                                    <span>132,600,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي المخصصات:</span>
                                    <span>70,980,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span>88,412,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>صافي الرواتب:</span>
                                    <span>115,168,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>تفاصيل الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span>21,918,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين:</span>
                                    <span>20,358,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة:</span>
                                    <span>30,537,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span>3,900,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span>11,699,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب التقارير -->
                <div id="reportsTab" class="tab-content">
                    <h3>تقارير الرواتب</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات التقرير</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>نوع التقرير:</label>
                                    <select id="reportType">
                                        <option>تقرير رواتب شهري</option>
                                        <option>تقرير رواتب حسب القسم</option>
                                        <option>تقرير المخصصات</option>
                                        <option>تقرير الاستقطاعات</option>
                                        <option>قسائم الرواتب</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الفترة:</label>
                                    <select id="reportPeriod">
                                        <option>ديسمبر 2024</option>
                                        <option>نوفمبر 2024</option>
                                        <option>أكتوبر 2024</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>تنسيق التصدير:</label>
                                    <select id="exportFormat">
                                        <option>PDF</option>
                                        <option>Excel</option>
                                        <option>Word</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="reportDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="generateReport()">📊 إنشاء التقرير</button>
                        <button class="btn btn-secondary" onclick="previewReport()">👁️ معاينة</button>
                        <button class="btn btn-primary" onclick="exportReport()">📤 تصدير</button>
                    </div>

                    <!-- معاينة التقرير -->
                    <div class="payslip">
                        <div class="payslip-header">
                            <h2>وزارة الشباب والرياضة</h2>
                            <h3>تقرير رواتب شهر ديسمبر 2024</h3>
                            <p>تاريخ التقرير: 2024/12/19</p>
                        </div>

                        <table class="employees-table">
                            <thead>
                                <tr>
                                    <th>ت</th>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم الكامل</th>
                                    <th>العنوان الوظيفي</th>
                                    <th>الراتب الأساسي</th>
                                    <th>المخصصات</th>
                                    <th>الاستقطاعات</th>
                                    <th>صافي الراتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>2024001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>محاسب أول</td>
                                    <td>850,000</td>
                                    <td>455,000</td>
                                    <td>566,750</td>
                                    <td>838,250</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>2024002</td>
                                    <td>فاطمة حسن محمود</td>
                                    <td>مدير مالي</td>
                                    <td>1,200,000</td>
                                    <td>650,000</td>
                                    <td>600,000</td>
                                    <td>1,250,000</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>2024003</td>
                                    <td>محمد عبدالله أحمد</td>
                                    <td>كاتب حسابات</td>
                                    <td>650,000</td>
                                    <td>300,000</td>
                                    <td>300,000</td>
                                    <td>650,000</td>
                                </tr>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td colspan="4">المجموع الكلي</td>
                                    <td>2,700,000</td>
                                    <td>1,405,000</td>
                                    <td>1,466,750</td>
                                    <td>2,738,250</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-SA');
            const timeStr = now.toLocaleTimeString('ar-SA');

            document.getElementById('currentDate').textContent = dateStr;
            document.getElementById('statusDate').textContent = `التاريخ: ${dateStr}`;
            document.getElementById('statusTime').textContent = `الوقت: ${timeStr}`;
        }

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin123') {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('mainContainer').style.display = 'block';
                updateDateTime();
                setInterval(updateDateTime, 1000);

                // رسالة ترحيب
                setTimeout(() => {
                    alert('تم تسجيل الدخول بنجاح! مرحباً بك في نظام محاسبة وزارة الشباب والرياضة');
                }, 500);
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('password').value = '';
            }
        }

        // عرض الوحدات
        function showModule(moduleName) {
            const modules = {
                'users-list': {
                    title: 'إدارة المستخدمين',
                    content: 'هنا يتم إدارة المستخدمين وصلاحياتهم مع تشفير آمن وحماية من الهجمات'
                },
                'user-groups': {
                    title: 'مجموعات المستخدمين',
                    content: 'هنا يتم إدارة مجموعات المستخدمين مع صلاحيات ديناميكية'
                },
                'permissions': {
                    title: 'الصلاحيات',
                    content: 'هنا يتم إدارة صلاحيات النظام (قراءة، إضافة، تعديل، حذف)'
                },
                'organization': {
                    title: 'معلومات المؤسسة',
                    content: 'هنا يتم إدارة معلومات وزارة الشباب والرياضة'
                },
                'departments': {
                    title: 'الدوائر',
                    content: 'هنا يتم إدارة دوائر الوزارة مثل دائرة الطب الرياضي'
                },
                'sections': {
                    title: 'الأقسام',
                    content: 'هنا يتم إدارة أقسام الدوائر مثل القسم الإداري'
                },
                'banks': {
                    title: 'المصارف',
                    content: 'هنا يتم إدارة المصارف وفروعها مع حسابات التشغيل والرواتب'
                },
                'chart-accounts': {
                    title: 'دليل الحسابات',
                    content: 'هنا يتم إدارة شجرة الحسابات المحاسبية متعددة المستويات'
                },
                'journal-entries': {
                    title: 'القيود اليومية',
                    content: 'هنا يتم إدخال وإدارة القيود المحاسبية مع نظام الموافقات'
                },
                'receipt-vouchers': {
                    title: 'سندات القبض',
                    content: 'هنا يتم إدارة سندات القبض مع ربط الحسابات البنكية'
                },
                'payment-vouchers': {
                    title: 'سندات الصرف',
                    content: 'هنا يتم إدارة سندات الصرف مع ربط الحسابات البنكية'
                },
                'employees': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'positions': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'payroll-processing': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'financial-reports': {
                    title: 'التقارير المالية',
                    content: 'هنا يتم عرض التقارير المالية: ميزان المراجعة، الميزانية العمومية، قائمة الدخل'
                },
                'payroll-reports': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                }
            };

            const module = modules[moduleName];
            if (module) {
                document.getElementById('modalTitle').textContent = module.title;

                if (module.isPayroll) {
                    // إخفاء المحتوى العادي وإظهار نظام الرواتب
                    document.getElementById('modalContent').style.display = 'none';
                    document.getElementById('payrollSystem').style.display = 'block';

                    // تحديد التبويب المناسب
                    if (moduleName === 'employees') {
                        showPayrollTab('employees');
                    } else if (moduleName === 'positions') {
                        showPayrollTab('positions');
                    } else if (moduleName === 'payroll-processing') {
                        showPayrollTab('processing');
                    } else if (moduleName === 'payroll-reports') {
                        showPayrollTab('reports');
                    }
                } else {
                    // إظهار المحتوى العادي وإخفاء نظام الرواتب
                    document.getElementById('modalContent').style.display = 'block';
                    document.getElementById('payrollSystem').style.display = 'none';
                    document.getElementById('modalContent').textContent = module.content;
                }

                document.getElementById('moduleModal').style.display = 'block';
            }
        }

        // وظائف نظام الرواتب
        function showPayrollTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            const tabButtons = document.querySelectorAll('.payroll-tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');

            // تفعيل الزر المناسب
            event.target.classList.add('active');
        }

        function updateSteps() {
            const grade = document.getElementById('grade').value;
            const stepSelect = document.getElementById('step');

            // مسح الخيارات الحالية
            stepSelect.innerHTML = '';

            // إضافة المراحل حسب الدرجة
            const steps = {
                '1': 15, // الدرجة الأولى - 15 مرحلة
                '2': 12, // الدرجة الثانية - 12 مرحلة
                '3': 10, // الدرجة الثالثة - 10 مراحل
                '4': 8   // الدرجة الرابعة - 8 مراحل
            };

            const stepCount = steps[grade] || 10;
            for (let i = 1; i <= stepCount; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `المرحلة ${i}`;
                stepSelect.appendChild(option);
            }

            // إعادة حساب الراتب
            calculateSalary();
        }

        function calculateSalary() {
            // الحصول على القيم
            const grade = parseInt(document.getElementById('grade').value);
            const step = parseInt(document.getElementById('step').value) || 1;
            const position = document.getElementById('position').value;
            const qualification = document.getElementById('qualification').value;

            // الرواتب الأساسية حسب الدرجة
            const basicSalaries = {
                1: 850000,
                2: 750000,
                3: 650000,
                4: 550000
            };

            // مبالغ المراحل
            const stepAmounts = {
                1: 25000,
                2: 20000,
                3: 15000,
                4: 12000
            };

            // حساب الراتب الأساسي
            const basicSalary = basicSalaries[grade] || 650000;
            const allowance = 75000; // علاوة ثابتة
            const salaryDiff = (step - 1) * (stepAmounts[grade] || 15000);
            const basicTotal = basicSalary + allowance + salaryDiff;

            // حساب المخصصات
            const positionAllowances = {
                'رئيس قسم': 150000,
                'مسؤول شعبة': 100000,
                'موظف': 50000
            };

            const qualificationAllowances = {
                'دكتوراه': 150000,
                'ماجستير إدارة أعمال': 120000,
                'بكالوريوس محاسبة': 100000,
                'دبلوم عالي': 80000
            };

            const positionAllowance = positionAllowances[position] || 50000;
            const familyAllowance = 50000;
            const childrenAllowance = 75000;
            const qualificationAllowance = qualificationAllowances[qualification] || 100000;
            const universityAllowance = 80000;

            const totalAllowances = positionAllowance + familyAllowance + childrenAllowance +
                                  qualificationAllowance + universityAllowance;

            // حساب إجمالي الراتب
            const grossSalary = basicTotal + totalAllowances;

            // حساب الاستقطاعات
            const incomeTax = grossSalary * 0.10; // 10% ضريبة دخل
            const employeeRetirement = grossSalary * 0.10; // 10% تقاعد موظفين
            const deptContribution = grossSalary * 0.15; // 15% مساهمة دائرة
            const socialProtection = 25000;
            const bankLoans = 50000;

            const totalDeductions = incomeTax + employeeRetirement + deptContribution +
                                  socialProtection + bankLoans;

            // حساب صافي الراتب
            const netSalary = grossSalary - totalDeductions;

            // تحديث العرض
            document.getElementById('basicSalary').textContent = basicSalary.toLocaleString();
            document.getElementById('allowanceAmount').textContent = allowance.toLocaleString();
            document.getElementById('salaryDiff').textContent = salaryDiff.toLocaleString();
            document.getElementById('basicTotal').textContent = basicTotal.toLocaleString();

            document.getElementById('positionAllowance').textContent = positionAllowance.toLocaleString();
            document.getElementById('familyAllowance').textContent = familyAllowance.toLocaleString();
            document.getElementById('childrenAllowance').textContent = childrenAllowance.toLocaleString();
            document.getElementById('qualificationAllowance').textContent = qualificationAllowance.toLocaleString();
            document.getElementById('universityAllowance').textContent = universityAllowance.toLocaleString();
            document.getElementById('totalAllowances').textContent = totalAllowances.toLocaleString();

            document.getElementById('incomeTax').textContent = Math.round(incomeTax).toLocaleString();
            document.getElementById('employeeRetirement').textContent = Math.round(employeeRetirement).toLocaleString();
            document.getElementById('deptContribution').textContent = Math.round(deptContribution).toLocaleString();
            document.getElementById('socialProtection').textContent = socialProtection.toLocaleString();
            document.getElementById('bankLoans').textContent = bankLoans.toLocaleString();
            document.getElementById('totalDeductions').textContent = Math.round(totalDeductions).toLocaleString();

            document.getElementById('grossSalary').textContent = grossSalary.toLocaleString();
            document.getElementById('netSalary').textContent = Math.round(netSalary).toLocaleString();
        }

        function saveEmployee() {
            const firstName = document.getElementById('firstName').value;
            const middleName = document.getElementById('middleName').value;
            const lastName = document.getElementById('lastName').value;

            alert(`تم حفظ بيانات الموظف: ${firstName} ${middleName} ${lastName} بنجاح!`);
        }

        function generatePayslip() {
            alert('تم إنشاء قسيمة الراتب بنجاح! سيتم تحميل الملف قريباً...');
        }

        function editEmployee(id) {
            alert(`تحرير بيانات الموظف رقم: ${id}`);
        }

        function viewPayslip(id) {
            alert(`عرض قسيمة راتب الموظف رقم: ${id}`);
        }

        function processPayroll() {
            const month = document.getElementById('payrollMonth').value;
            const year = document.getElementById('payrollYear').value;

            alert(`تم بدء معالجة رواتب شهر ${month}/${year} بنجاح!`);
        }

        function validatePayroll() {
            alert('تم التحقق من صحة البيانات بنجاح! جميع البيانات صحيحة ✅');
        }

        function approvePayroll() {
            alert('تم اعتماد الرواتب بنجاح! ✅');
        }

        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            alert(`تم إنشاء ${reportType} بنجاح!`);
        }

        function previewReport() {
            alert('معاينة التقرير متاحة أدناه 👁️');
        }

        function exportReport() {
            const format = document.getElementById('exportFormat').value;
            alert(`تم تصدير التقرير بصيغة ${format} بنجاح! 📤`);
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('moduleModal').style.display = 'none';
        }

        // عرض معلومات البرنامج
        function showAbout() {
            alert('نظام محاسبة وزارة الشباب والرياضة\nالإصدار 1.0.0\nتم التطوير باستخدام VB.NET\nجميع الحقوق محفوظة © 2024');
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('moduleModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // تحديث التاريخ والوقت عند تحميل الصفحة
        updateDateTime();

        // تحديث حسابات الراتب عند تحميل الصفحة
        setTimeout(() => {
            if (document.getElementById('basicSalary')) {
                calculateSalary();
            }
        }, 1000);
    </script>
</body>
</html>
