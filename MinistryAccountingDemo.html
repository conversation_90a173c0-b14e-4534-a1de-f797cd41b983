<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاسبة وزارة الشباب والرياضة - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .title {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: bold;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: #45a049;
        }

        .default-credentials {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .default-credentials h4 {
            color: #2196F3;
            margin-bottom: 10px;
        }

        .main-container {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .nav-menu {
            background: #34495e;
            padding: 0;
            overflow-x: auto;
        }

        .nav-menu ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu li {
            position: relative;
        }

        .nav-menu a {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            transition: background 0.3s;
            white-space: nowrap;
        }

        .nav-menu a:hover {
            background: #2c3e50;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #2c3e50;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: none;
            z-index: 1000;
        }

        .nav-menu li:hover .dropdown {
            display: block;
        }

        .content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .welcome-card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .welcome-card p {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 1000px;
            position: relative;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-bar {
            background: #ecf0f1;
            padding: 10px 20px;
            border-top: 1px solid #bdc3c7;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
        }

        /* أنماط نظام الرواتب */
        .payroll-container {
            display: none;
        }

        .payroll-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .payroll-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 20px;
            gap: 5px;
        }

        .payroll-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .payroll-tab.active {
            background: #4CAF50;
            color: white;
        }

        .payroll-tab:hover {
            background: #e9ecef;
        }

        .payroll-tab.active:hover {
            background: #45a049;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .tab-content.active {
            display: block;
        }

        .employee-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid #4CAF50;
        }

        .form-section h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group-inline {
            display: flex;
            flex-direction: column;
        }

        .form-group-inline label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }

        .form-group-inline input,
        .form-group-inline select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .salary-calculation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .salary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .salary-section {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
        }

        .salary-section h5 {
            margin-bottom: 10px;
            font-size: 14px;
            opacity: 0.9;
        }

        .salary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .salary-total {
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }

        .employees-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .employees-table th,
        .employees-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }

        .employees-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .employees-table tr:hover {
            background: #f8f9fa;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .payslip {
            background: white;
            padding: 30px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .payslip-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .payslip-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .payslip-details {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        /* أنماط إدارة المستخدمين */
        .users-container {
            display: none;
        }

        .users-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .users-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .user-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .user-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .section-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .section-icon {
            font-size: 48px;
            margin-bottom: 10px;
            display: block;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }

        .section-content {
            padding: 25px;
        }

        .user-form {
            margin-bottom: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-grid-full {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            font-size: 14px;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .input-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-display {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px 12px;
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-input-display:hover {
            border-color: #4CAF50;
            background: #f0f8f0;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #4CAF50;
            margin-left: 10px;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .users-table th,
        .users-table td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #e1e5e9;
        }

        .users-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }

        .users-table tr:hover {
            background: #f8f9fa;
        }

        .users-table tr:last-child td {
            border-bottom: none;
        }

        .users-table tr.selected {
            background: #e3f2fd !important;
            border-left: 4px solid #2196F3;
        }

        .users-table tr {
            cursor: pointer;
            transition: all 0.3s;
        }

        .users-table tbody tr:hover {
            background: #f0f8ff;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
        }

        .status-admin {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-user {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
        }

        .btn-edit {
            background: #2196F3;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-edit:hover {
            background: #1976D2;
        }

        .btn-delete {
            background: #f44336;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn-delete:hover {
            background: #d32f2f;
        }

        .section-actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        /* أنماط نظام الإعدادات */
        .settings-container {
            display: none;
        }

        .settings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .settings-tabs {
            display: flex;
            flex-wrap: wrap;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 20px;
            gap: 10px;
        }

        .settings-tab {
            flex: 1;
            min-width: 200px;
            padding: 12px 15px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            text-align: center;
            font-size: 14px;
        }

        .settings-tab:hover {
            border-color: #4CAF50;
            background: #f0f8f0;
        }

        .settings-tab.active {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .settings-content {
            display: none;
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .settings-content.active {
            display: block;
        }

        .settings-form {
            margin-bottom: 25px;
        }

        .settings-form h4 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .settings-field {
            display: flex;
            flex-direction: column;
        }

        .settings-field label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
        }

        .settings-field input,
        .settings-field select,
        .settings-field textarea {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .settings-field input:focus,
        .settings-field select:focus,
        .settings-field textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }

        .settings-field textarea {
            resize: vertical;
            min-height: 100px;
        }

        .settings-actions {
            text-align: center;
            margin: 25px 0;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .settings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .settings-table th,
        .settings-table td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #e1e5e9;
        }

        .settings-table th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }

        .settings-table tr:hover {
            background: #f8f9fa;
        }

        .settings-table tr:last-child td {
            border-bottom: none;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .nav-menu ul {
                flex-direction: column;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: 10px;
            }

            .salary-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .payroll-tabs {
                flex-direction: column;
            }

            .payslip-info,
            .payslip-details {
                grid-template-columns: 1fr;
            }

            .users-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .settings-tabs {
                flex-direction: column;
            }

            .settings-tab {
                min-width: auto;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- نموذج تسجيل الدخول -->
    <div id="loginContainer" class="login-container">
        <div class="login-form">
            <div class="logo">وش</div>
            <h2 class="title">نظام محاسبة وزارة الشباب والرياضة</h2>
            <p class="subtitle">نظام محاسبة ورواتب متطور وذكي</p>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" required>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" required>
                </div>

                <button type="submit" class="login-btn">تسجيل الدخول</button>
            </form>

            <div class="default-credentials">
                <h4>بيانات تسجيل الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
        </div>
    </div>

    <!-- النموذج الرئيسي -->
    <div id="mainContainer" class="main-container">
        <!-- رأس الصفحة -->
        <header class="header">
            <h1>نظام محاسبة وزارة الشباب والرياضة</h1>
            <div class="user-info">
                <span id="currentUser">المستخدم: admin</span>
                <span id="currentDate"></span>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </header>

        <!-- شريط التنقل -->
        <nav class="nav-menu">
            <ul>
                <li>
                    <a href="#" onclick="showModule('users-list')">إدارة المستخدمين</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('users-list')">المستخدمون</a>
                        <a href="#" onclick="showModule('user-groups')">مجموعات المستخدمين</a>
                        <a href="#" onclick="showModule('permissions')">الصلاحيات</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('settings-main')">الإعدادات والتهيئة</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('organization')">📋 معلومات المؤسسة</a>
                        <a href="#" onclick="showModule('departments')">🏢 دليل الدوائر</a>
                        <a href="#" onclick="showModule('sections')">📁 دليل الأقسام</a>
                        <a href="#" onclick="showModule('divisions')">🗂️ دليل الشعب</a>
                        <a href="#" onclick="showModule('banks')">🏦 دليل المصارف</a>
                        <a href="#" onclick="showModule('bank-branches')">🏪 فروع المصارف</a>
                        <a href="#" onclick="showModule('cashboxes')">💰 دليل الصناديق</a>
                        <a href="#" onclick="showModule('currencies')">💱 دليل العملات</a>
                        <a href="#" onclick="showModule('accounting-periods')">📅 الفترات المحاسبية</a>
                        <a href="#" onclick="showModule('final-accounts')">📊 الحسابات الختامية</a>
                        <a href="#" onclick="showModule('opening-balances')">💼 الأرصدة الافتتاحية</a>
                        <a href="#" onclick="showModule('backup-settings')">💾 إعدادات النسخ الاحتياطي</a>
                        <a href="#" onclick="showModule('database-connection')">🔗 إعدادات قاعدة البيانات</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('chart-accounts')">المحاسبة</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('chart-accounts')">دليل الحسابات</a>
                        <a href="#" onclick="showModule('journal-entries')">القيود اليومية</a>
                        <a href="#" onclick="showModule('receipt-vouchers')">سندات القبض</a>
                        <a href="#" onclick="showModule('payment-vouchers')">سندات الصرف</a>
                        <a href="#" onclick="showModule('trial-balance')">ميزان المراجعة</a>
                        <a href="#" onclick="showModule('balance-sheet')">الميزانية العمومية</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('employees')">الرواتب</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('employees')">الموظفون</a>
                        <a href="#" onclick="showModule('positions')">المناصب</a>
                        <a href="#" onclick="showModule('payroll-processing')">معالجة الرواتب</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('financial-reports')">التقارير</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('financial-reports')">التقارير المالية</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                        <a href="#" onclick="showModule('custom-reports')">التقارير المخصصة</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showAbout()">حول البرنامج</a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <div class="welcome-card">
                <h2>مرحباً بك في نظام محاسبة وزارة الشباب والرياضة</h2>
                <p>نظام محاسبة ورواتب متطور وذكي مصمم خصيصاً لوزارة الشباب والرياضة</p>
                <p>تم تطويره باستخدام أحدث التقنيات البرمجية والمحاسبية الحديثة</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>نظام شامل لإدارة المستخدمين والصلاحيات مع تشفير آمن وصلاحيات ديناميكية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>الإعدادات والتهيئة</h3>
                    <p>إدارة الهيكل التنظيمي والمصارف والعملات مع نظام النسخ الاحتياطي التلقائي</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>النظام المحاسبي</h3>
                    <p>شجرة حسابات متعددة المستويات مع القيود اليومية والتقارير المحاسبية الشاملة</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>نظام الرواتب</h3>
                    <p>حساب تلقائي للرواتب مع جميع المخصصات والاستقطاعات وفق المعايير الحكومية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>التقارير والتحليلات</h3>
                    <p>تقارير مالية ومحاسبية شاملة مع إمكانية التصدير إلى PDF و Excel</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>دعم اللغة العربية</h3>
                    <p>واجهة RTL كاملة مع دعم الخطوط العربية والتواريخ الهجرية والميلادية</p>
                </div>
            </div>
        </main>

        <!-- شريط الحالة -->
        <div class="status-bar">
            <span>جاهز</span>
            <span id="statusUser">المستخدم: admin</span>
            <span id="statusDate"></span>
            <span id="statusTime"></span>
        </div>
    </div>

    <!-- نافذة منبثقة للوحدات -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">عنوان الوحدة</h2>
            <div id="modalContent">محتوى الوحدة</div>

            <!-- نظام الإعدادات والتهيئة -->
            <div id="settingsSystem" class="settings-container">
                <div class="settings-header">
                    <h2>⚙️ نظام الإعدادات والتهيئة - وزارة الشباب والرياضة</h2>
                    <p>مركز التحكم الشامل لإعدادات النظام والبيانات الأساسية</p>
                </div>

                <div class="settings-tabs">
                    <div class="settings-tab active" onclick="showSettingsTab('organization')">📋 معلومات المؤسسة</div>
                    <div class="settings-tab" onclick="showSettingsTab('departments')">🏢 دليل الدوائر</div>
                    <div class="settings-tab" onclick="showSettingsTab('sections')">📁 دليل الأقسام</div>
                    <div class="settings-tab" onclick="showSettingsTab('divisions')">🗂️ دليل الشعب</div>
                    <div class="settings-tab" onclick="showSettingsTab('banks')">🏦 دليل المصارف</div>
                    <div class="settings-tab" onclick="showSettingsTab('bank-branches')">🏪 فروع المصارف</div>
                    <div class="settings-tab" onclick="showSettingsTab('cashboxes')">💰 دليل الصناديق</div>
                    <div class="settings-tab" onclick="showSettingsTab('currencies')">💱 دليل العملات</div>
                    <div class="settings-tab" onclick="showSettingsTab('accounting-periods')">📅 الفترات المحاسبية</div>
                    <div class="settings-tab" onclick="showSettingsTab('final-accounts')">📊 الحسابات الختامية</div>
                    <div class="settings-tab" onclick="showSettingsTab('opening-balances')">💼 الأرصدة الافتتاحية</div>
                </div>

                <!-- معلومات المؤسسة -->
                <div id="organizationContent" class="settings-content active">
                    <div class="settings-form">
                        <h4>📋 البيانات الأساسية للمؤسسة</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>اسم المؤسسة (عربي):</label>
                                <input type="text" id="orgNameAr" value="وزارة الشباب والرياضة">
                            </div>
                            <div class="settings-field">
                                <label>اسم المؤسسة (إنجليزي):</label>
                                <input type="text" id="orgNameEn" value="Ministry of Youth and Sports">
                            </div>
                            <div class="settings-field">
                                <label>الرقم الرسمي:</label>
                                <input type="text" id="orgNumber" value="MYS-2024-001">
                            </div>
                            <div class="settings-field">
                                <label>الرمز التعريفي:</label>
                                <input type="text" id="orgCode" value="MYS">
                            </div>
                            <div class="settings-field">
                                <label>العنوان الكامل:</label>
                                <textarea id="orgAddress">بغداد - الكرادة - شارع الكندي - مجمع الوزارات</textarea>
                            </div>
                            <div class="settings-field">
                                <label>الرمز البريدي:</label>
                                <input type="text" id="orgPostalCode" value="10001">
                            </div>
                            <div class="settings-field">
                                <label>رقم الهاتف الرئيسي:</label>
                                <input type="text" id="orgPhone" value="+964-1-7901234">
                            </div>
                            <div class="settings-field">
                                <label>رقم الفاكس:</label>
                                <input type="text" id="orgFax" value="+964-1-7905678">
                            </div>
                            <div class="settings-field">
                                <label>البريد الإلكتروني:</label>
                                <input type="email" id="orgEmail" value="<EMAIL>">
                            </div>
                            <div class="settings-field">
                                <label>الموقع الرسمي:</label>
                                <input type="url" id="orgWebsite" value="https://www.mys.gov.iq">
                            </div>
                            <div class="settings-field">
                                <label>تاريخ التأسيس:</label>
                                <input type="date" id="orgFoundingDate" value="2003-05-01">
                            </div>
                            <div class="settings-field">
                                <label>الوزير المسؤول:</label>
                                <input type="text" id="orgMinister" value="معالي الوزير أحمد الخزعلي">
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="saveOrganizationInfo()">💾 حفظ البيانات</button>
                            <button class="btn btn-secondary" onclick="resetOrganizationForm()">🔄 إعادة تعيين</button>
                        </div>
                    </div>
                </div>

                <!-- دليل الدوائر -->
                <div id="departmentsContent" class="settings-content">
                    <div class="settings-form">
                        <h4>🏢 إضافة دائرة جديدة</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الدائرة:</label>
                                <input type="text" id="deptCode" value="DEPT001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الدائرة:</label>
                                <input type="text" id="deptName" placeholder="مثال: دائرة الطب الرياضي">
                            </div>
                            <div class="settings-field">
                                <label>مدير الدائرة:</label>
                                <input type="text" id="deptManager" placeholder="اسم مدير الدائرة">
                            </div>
                            <div class="settings-field">
                                <label>هاتف الدائرة:</label>
                                <input type="text" id="deptPhone" placeholder="+964-1-xxxxxxx">
                            </div>
                            <div class="settings-field">
                                <label>الموازنة المخصصة:</label>
                                <input type="number" id="deptBudget" placeholder="المبلغ بالدينار العراقي">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="deptStatus">
                                    <option value="active">نشطة</option>
                                    <option value="inactive">غير نشطة</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>وصف الدائرة:</label>
                                <textarea id="deptDescription" placeholder="وصف تفصيلي لمهام ونشاطات الدائرة"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addDepartment()">➕ إضافة الدائرة</button>
                            <button class="btn btn-secondary" onclick="clearDepartmentForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الدائرة</th>
                                <th>اسم الدائرة</th>
                                <th>المدير</th>
                                <th>الهاتف</th>
                                <th>الموازنة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="departmentsTableBody">
                            <tr>
                                <td>DEPT001</td>
                                <td>دائرة الطب الرياضي</td>
                                <td>د. محمد علي حسن</td>
                                <td>+964-1-7901111</td>
                                <td>500,000,000</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editDepartment('DEPT001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteDepartment('DEPT001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>DEPT002</td>
                                <td>دائرة الشؤون الإدارية والمالية</td>
                                <td>أ. فاطمة أحمد محمود</td>
                                <td>+964-1-7902222</td>
                                <td>750,000,000</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editDepartment('DEPT002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteDepartment('DEPT002')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>DEPT003</td>
                                <td>دائرة التخطيط والمتابعة</td>
                                <td>م. عبدالله سالم</td>
                                <td>+964-1-7903333</td>
                                <td>300,000,000</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editDepartment('DEPT003')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteDepartment('DEPT003')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- دليل الأقسام -->
                <div id="sectionsContent" class="settings-content">
                    <div class="settings-form">
                        <h4>📁 إضافة قسم جديد</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز القسم:</label>
                                <input type="text" id="sectionCode" value="SEC001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم القسم:</label>
                                <input type="text" id="sectionName" placeholder="مثال: القسم الإداري">
                            </div>
                            <div class="settings-field">
                                <label>الدائرة التابع لها:</label>
                                <select id="sectionDepartment">
                                    <option value="DEPT001">دائرة الطب الرياضي</option>
                                    <option value="DEPT002">دائرة الشؤون الإدارية والمالية</option>
                                    <option value="DEPT003">دائرة التخطيط والمتابعة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>رئيس القسم:</label>
                                <input type="text" id="sectionHead" placeholder="اسم رئيس القسم">
                            </div>
                            <div class="settings-field">
                                <label>نائب رئيس القسم:</label>
                                <input type="text" id="sectionDeputy" placeholder="اسم نائب رئيس القسم">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="sectionStatus">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>المهام والمسؤوليات:</label>
                                <textarea id="sectionTasks" placeholder="وصف مهام ومسؤوليات القسم"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addSection()">➕ إضافة القسم</button>
                            <button class="btn btn-secondary" onclick="clearSectionForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز القسم</th>
                                <th>اسم القسم</th>
                                <th>الدائرة</th>
                                <th>رئيس القسم</th>
                                <th>نائب الرئيس</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="sectionsTableBody">
                            <tr>
                                <td>SEC001</td>
                                <td>القسم الإداري</td>
                                <td>دائرة الشؤون الإدارية والمالية</td>
                                <td>أ. سارة محمد</td>
                                <td>أ. أحمد علي</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editSection('SEC001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteSection('SEC001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>SEC002</td>
                                <td>قسم المحاسبة والموازنة</td>
                                <td>دائرة الشؤون الإدارية والمالية</td>
                                <td>م. خالد حسين</td>
                                <td>أ. نور فاضل</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editSection('SEC002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteSection('SEC002')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- دليل الشعب -->
                <div id="divisionsContent" class="settings-content">
                    <div class="settings-form">
                        <h4>🗂️ إضافة شعبة جديدة</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الشعبة:</label>
                                <input type="text" id="divisionCode" value="DIV001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الشعبة:</label>
                                <input type="text" id="divisionName" placeholder="مثال: شعبة الحسابات">
                            </div>
                            <div class="settings-field">
                                <label>القسم التابع له:</label>
                                <select id="divisionSection">
                                    <option value="SEC001">القسم الإداري</option>
                                    <option value="SEC002">قسم المحاسبة والموازنة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>مسؤول الشعبة:</label>
                                <input type="text" id="divisionHead" placeholder="اسم مسؤول الشعبة">
                            </div>
                            <div class="settings-field">
                                <label>عدد الموظفين:</label>
                                <input type="number" id="divisionEmployees" placeholder="عدد الموظفين">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="divisionStatus">
                                    <option value="active">نشطة</option>
                                    <option value="inactive">غير نشطة</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>الاختصاصات:</label>
                                <textarea id="divisionSpecialty" placeholder="وصف اختصاصات ومهام الشعبة"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addDivision()">➕ إضافة الشعبة</button>
                            <button class="btn btn-secondary" onclick="clearDivisionForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الشعبة</th>
                                <th>اسم الشعبة</th>
                                <th>القسم</th>
                                <th>المسؤول</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="divisionsTableBody">
                            <tr>
                                <td>DIV001</td>
                                <td>شعبة الحسابات والمدفوعات</td>
                                <td>قسم المحاسبة والموازنة</td>
                                <td>أ. زينب علي</td>
                                <td>8</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editDivision('DIV001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteDivision('DIV001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>DIV002</td>
                                <td>شعبة الرواتب والمخصصات</td>
                                <td>قسم المحاسبة والموازنة</td>
                                <td>م. حسام محمد</td>
                                <td>6</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editDivision('DIV002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteDivision('DIV002')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- دليل المصارف -->
                <div id="banksContent" class="settings-content">
                    <div class="settings-form">
                        <h4>🏦 إضافة مصرف جديد</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز المصرف:</label>
                                <input type="text" id="bankCode" value="BANK001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم المصرف:</label>
                                <input type="text" id="bankName" placeholder="مثال: المصرف العراقي للتجارة">
                            </div>
                            <div class="settings-field">
                                <label>الاختصار:</label>
                                <input type="text" id="bankAbbr" placeholder="مثال: TBI">
                            </div>
                            <div class="settings-field">
                                <label>نوع المصرف:</label>
                                <select id="bankType">
                                    <option value="government">حكومي</option>
                                    <option value="private">أهلي</option>
                                    <option value="islamic">إسلامي</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>رقم حساب التشغيل:</label>
                                <input type="text" id="bankOperatingAccount" placeholder="رقم الحساب">
                            </div>
                            <div class="settings-field">
                                <label>رقم حساب الرواتب:</label>
                                <input type="text" id="bankPayrollAccount" placeholder="رقم الحساب">
                            </div>
                            <div class="settings-field">
                                <label>رقم IBAN التشغيل:</label>
                                <input type="text" id="bankOperatingIBAN" placeholder="***********************">
                            </div>
                            <div class="settings-field">
                                <label>رقم IBAN الرواتب:</label>
                                <input type="text" id="bankPayrollIBAN" placeholder="***********************">
                            </div>
                            <div class="settings-field">
                                <label>المفوض بالتوقيع:</label>
                                <input type="text" id="bankAuthorizedSignatory" placeholder="اسم المفوض">
                            </div>
                            <div class="settings-field">
                                <label>حد السحب اليومي:</label>
                                <input type="number" id="bankDailyLimit" placeholder="المبلغ بالدينار">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="bankStatus">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>هاتف المصرف:</label>
                                <input type="text" id="bankPhone" placeholder="+964-1-xxxxxxx">
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addBank()">➕ إضافة المصرف</button>
                            <button class="btn btn-secondary" onclick="clearBankForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز المصرف</th>
                                <th>اسم المصرف</th>
                                <th>النوع</th>
                                <th>حساب التشغيل</th>
                                <th>حساب الرواتب</th>
                                <th>المفوض</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="banksTableBody">
                            <tr>
                                <td>BANK001</td>
                                <td>المصرف العراقي للتجارة</td>
                                <td>حكومي</td>
                                <td>*********</td>
                                <td>*********</td>
                                <td>أ. محمد الخزعلي</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editBank('BANK001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteBank('BANK001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>BANK002</td>
                                <td>مصرف الرافدين</td>
                                <td>حكومي</td>
                                <td>*********</td>
                                <td>*********</td>
                                <td>د. فاطمة الزهراء</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editBank('BANK002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteBank('BANK002')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- فروع المصارف -->
                <div id="bank-branchesContent" class="settings-content">
                    <div class="settings-form">
                        <h4>🏪 إضافة فرع مصرف جديد</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الفرع:</label>
                                <input type="text" id="branchCode" value="BR001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الفرع:</label>
                                <input type="text" id="branchName" placeholder="مثال: فرع بغداد المركزي">
                            </div>
                            <div class="settings-field">
                                <label>المصرف التابع له:</label>
                                <select id="branchBank">
                                    <option value="BANK001">المصرف العراقي للتجارة</option>
                                    <option value="BANK002">مصرف الرافدين</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>المحافظة:</label>
                                <select id="branchProvince">
                                    <option value="baghdad">بغداد</option>
                                    <option value="basra">البصرة</option>
                                    <option value="erbil">أربيل</option>
                                    <option value="mosul">الموصل</option>
                                    <option value="najaf">النجف</option>
                                    <option value="karbala">كربلاء</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>مدير الفرع:</label>
                                <input type="text" id="branchManager" placeholder="اسم مدير الفرع">
                            </div>
                            <div class="settings-field">
                                <label>هاتف الفرع:</label>
                                <input type="text" id="branchPhone" placeholder="+964-1-xxxxxxx">
                            </div>
                            <div class="settings-field">
                                <label>رمز التحويل:</label>
                                <input type="text" id="branchSwiftCode" placeholder="SWIFT Code">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="branchStatus">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>العنوان الكامل:</label>
                                <textarea id="branchAddress" placeholder="العنوان التفصيلي للفرع"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addBranch()">➕ إضافة الفرع</button>
                            <button class="btn btn-secondary" onclick="clearBranchForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الفرع</th>
                                <th>اسم الفرع</th>
                                <th>المصرف</th>
                                <th>المحافظة</th>
                                <th>المدير</th>
                                <th>الهاتف</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="branchesTableBody">
                            <tr>
                                <td>BR001</td>
                                <td>فرع بغداد المركزي</td>
                                <td>المصرف العراقي للتجارة</td>
                                <td>بغداد</td>
                                <td>أ. علي حسن</td>
                                <td>+964-1-7801111</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editBranch('BR001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteBranch('BR001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>BR002</td>
                                <td>فرع البصرة الرئيسي</td>
                                <td>مصرف الرافدين</td>
                                <td>البصرة</td>
                                <td>د. سارة محمد</td>
                                <td>+964-40-123456</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editBranch('BR002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteBranch('BR002')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- دليل الصناديق -->
                <div id="cashboxesContent" class="settings-content">
                    <div class="settings-form">
                        <h4>💰 إضافة صندوق جديد</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الصندوق:</label>
                                <input type="text" id="cashboxCode" value="CASH001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الصندوق:</label>
                                <input type="text" id="cashboxName" placeholder="مثال: صندوق النقدية الرئيسي">
                            </div>
                            <div class="settings-field">
                                <label>نوع الصندوق:</label>
                                <select id="cashboxType">
                                    <option value="main">رئيسي</option>
                                    <option value="department">دائرة</option>
                                    <option value="project">مشروع</option>
                                    <option value="emergency">طوارئ</option>
                                    <option value="custody">عهدة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>أمين الصندوق:</label>
                                <input type="text" id="cashboxCustodian" placeholder="اسم أمين الصندوق">
                            </div>
                            <div class="settings-field">
                                <label>الرصيد الافتتاحي:</label>
                                <input type="number" id="cashboxOpeningBalance" placeholder="المبلغ بالدينار">
                            </div>
                            <div class="settings-field">
                                <label>الحد الأقصى:</label>
                                <input type="number" id="cashboxMaxLimit" placeholder="الحد الأقصى للرصيد">
                            </div>
                            <div class="settings-field">
                                <label>الدائرة التابع لها:</label>
                                <select id="cashboxDepartment">
                                    <option value="DEPT001">دائرة الطب الرياضي</option>
                                    <option value="DEPT002">دائرة الشؤون الإدارية والمالية</option>
                                    <option value="DEPT003">دائرة التخطيط والمتابعة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="cashboxStatus">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>ملاحظات:</label>
                                <textarea id="cashboxNotes" placeholder="ملاحظات حول الصندوق"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addCashbox()">➕ إضافة الصندوق</button>
                            <button class="btn btn-secondary" onclick="clearCashboxForm()">🔄 مسح النموذج</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الصندوق</th>
                                <th>اسم الصندوق</th>
                                <th>النوع</th>
                                <th>أمين الصندوق</th>
                                <th>الرصيد الحالي</th>
                                <th>الحد الأقصى</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="cashboxesTableBody">
                            <tr>
                                <td>CASH001</td>
                                <td>صندوق النقدية الرئيسي</td>
                                <td>رئيسي</td>
                                <td>أ. محمد علي</td>
                                <td>5,000,000</td>
                                <td>10,000,000</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editCashbox('CASH001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteCashbox('CASH001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>CASH002</td>
                                <td>صندوق دائرة الطب الرياضي</td>
                                <td>دائرة</td>
                                <td>د. فاطمة حسن</td>
                                <td>1,500,000</td>
                                <td>3,000,000</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editCashbox('CASH002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteCashbox('CASH002')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- دليل العملات -->
                <div id="currenciesContent" class="settings-content">
                    <div class="settings-form">
                        <h4>💱 إضافة عملة جديدة</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز العملة:</label>
                                <input type="text" id="currencyCode" value="USD" placeholder="مثال: USD">
                            </div>
                            <div class="settings-field">
                                <label>اسم العملة (عربي):</label>
                                <input type="text" id="currencyNameAr" placeholder="مثال: الدولار الأمريكي">
                            </div>
                            <div class="settings-field">
                                <label>اسم العملة (إنجليزي):</label>
                                <input type="text" id="currencyNameEn" placeholder="مثال: US Dollar">
                            </div>
                            <div class="settings-field">
                                <label>الرمز:</label>
                                <input type="text" id="currencySymbol" placeholder="مثال: $">
                            </div>
                            <div class="settings-field">
                                <label>سعر الصرف مقابل الدينار:</label>
                                <input type="number" step="0.01" id="currencyExchangeRate" placeholder="مثال: 1310.50">
                            </div>
                            <div class="settings-field">
                                <label>عدد الخانات العشرية:</label>
                                <select id="currencyDecimals">
                                    <option value="0">0</option>
                                    <option value="2" selected>2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>العملة الافتراضية:</label>
                                <select id="currencyDefault">
                                    <option value="no">لا</option>
                                    <option value="yes">نعم</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="currencyStatus">
                                    <option value="active">نشطة</option>
                                    <option value="inactive">غير نشطة</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>ملاحظات:</label>
                                <textarea id="currencyNotes" placeholder="ملاحظات حول العملة"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addCurrency()">➕ إضافة العملة</button>
                            <button class="btn btn-secondary" onclick="clearCurrencyForm()">🔄 مسح النموذج</button>
                            <button class="btn btn-secondary" onclick="updateExchangeRates()">📈 تحديث أسعار الصرف</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز العملة</th>
                                <th>اسم العملة</th>
                                <th>الرمز</th>
                                <th>سعر الصرف</th>
                                <th>الخانات العشرية</th>
                                <th>افتراضية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="currenciesTableBody">
                            <tr>
                                <td>IQD</td>
                                <td>الدينار العراقي</td>
                                <td>د.ع</td>
                                <td>1.00</td>
                                <td>0</td>
                                <td>نعم</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editCurrency('IQD')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteCurrency('IQD')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>USD</td>
                                <td>الدولار الأمريكي</td>
                                <td>$</td>
                                <td>1310.50</td>
                                <td>2</td>
                                <td>لا</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editCurrency('USD')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteCurrency('USD')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>EUR</td>
                                <td>اليورو الأوروبي</td>
                                <td>€</td>
                                <td>1425.75</td>
                                <td>2</td>
                                <td>لا</td>
                                <td><span class="status-active">نشطة</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editCurrency('EUR')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteCurrency('EUR')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- الفترات المحاسبية -->
                <div id="accounting-periodsContent" class="settings-content">
                    <div class="settings-form">
                        <h4>📅 إضافة فترة محاسبية جديدة</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الفترة:</label>
                                <input type="text" id="periodCode" value="PER2024" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الفترة:</label>
                                <input type="text" id="periodName" placeholder="مثال: السنة المالية 2024">
                            </div>
                            <div class="settings-field">
                                <label>نوع الفترة:</label>
                                <select id="periodType">
                                    <option value="yearly">سنوية</option>
                                    <option value="monthly">شهرية</option>
                                    <option value="quarterly">ربعية</option>
                                    <option value="custom">مخصصة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>تاريخ البداية:</label>
                                <input type="date" id="periodStartDate" value="2024-01-01">
                            </div>
                            <div class="settings-field">
                                <label>تاريخ النهاية:</label>
                                <input type="date" id="periodEndDate" value="2024-12-31">
                            </div>
                            <div class="settings-field">
                                <label>حالة الفترة:</label>
                                <select id="periodStatus">
                                    <option value="open">مفتوحة</option>
                                    <option value="closed">مغلقة</option>
                                    <option value="archived">مؤرشفة</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>الفترة الافتراضية:</label>
                                <select id="periodDefault">
                                    <option value="no">لا</option>
                                    <option value="yes">نعم</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>السنة المالية:</label>
                                <input type="number" id="periodFiscalYear" value="2024" min="2020" max="2030">
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>وصف الفترة:</label>
                                <textarea id="periodDescription" placeholder="وصف تفصيلي للفترة المحاسبية"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addPeriod()">➕ إضافة الفترة</button>
                            <button class="btn btn-secondary" onclick="clearPeriodForm()">🔄 مسح النموذج</button>
                            <button class="btn btn-danger" onclick="closePeriod()">🔒 إقفال الفترة</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الفترة</th>
                                <th>اسم الفترة</th>
                                <th>النوع</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ النهاية</th>
                                <th>الحالة</th>
                                <th>افتراضية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="periodsTableBody">
                            <tr>
                                <td>PER2024</td>
                                <td>السنة المالية 2024</td>
                                <td>سنوية</td>
                                <td>2024-01-01</td>
                                <td>2024-12-31</td>
                                <td><span class="status-active">مفتوحة</span></td>
                                <td>نعم</td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editPeriod('PER2024')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deletePeriod('PER2024')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>PER2023</td>
                                <td>السنة المالية 2023</td>
                                <td>سنوية</td>
                                <td>2023-01-01</td>
                                <td>2023-12-31</td>
                                <td><span class="status-inactive">مغلقة</span></td>
                                <td>لا</td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editPeriod('PER2023')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deletePeriod('PER2023')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- الحسابات الختامية -->
                <div id="final-accountsContent" class="settings-content">
                    <div class="settings-form">
                        <h4>📊 إعداد الحسابات الختامية</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رمز الحساب الختامي:</label>
                                <input type="text" id="finalAccountCode" value="FA001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>اسم الحساب:</label>
                                <input type="text" id="finalAccountName" placeholder="مثال: حساب الأرباح والخسائر">
                            </div>
                            <div class="settings-field">
                                <label>نوع الحساب:</label>
                                <select id="finalAccountType">
                                    <option value="income">الإيرادات</option>
                                    <option value="expense">المصروفات</option>
                                    <option value="asset">الأصول</option>
                                    <option value="liability">الخصوم</option>
                                    <option value="equity">حقوق الملكية</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>طبيعة الحساب:</label>
                                <select id="finalAccountNature">
                                    <option value="debit">مدين</option>
                                    <option value="credit">دائن</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>ترتيب العرض:</label>
                                <input type="number" id="finalAccountOrder" placeholder="ترتيب الحساب في التقرير">
                            </div>
                            <div class="settings-field">
                                <label>الحالة:</label>
                                <select id="finalAccountStatus">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>وصف الحساب:</label>
                                <textarea id="finalAccountDescription" placeholder="وصف تفصيلي للحساب الختامي"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addFinalAccount()">➕ إضافة الحساب</button>
                            <button class="btn btn-secondary" onclick="clearFinalAccountForm()">🔄 مسح النموذج</button>
                            <button class="btn btn-primary" onclick="generateFinalStatements()">📊 إنشاء القوائم المالية</button>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الطبيعة</th>
                                <th>الترتيب</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="finalAccountsTableBody">
                            <tr>
                                <td>FA001</td>
                                <td>حساب الأرباح والخسائر</td>
                                <td>حقوق الملكية</td>
                                <td>دائن</td>
                                <td>1</td>
                                <td>15,000,000</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editFinalAccount('FA001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteFinalAccount('FA001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>FA002</td>
                                <td>إجمالي الإيرادات</td>
                                <td>الإيرادات</td>
                                <td>دائن</td>
                                <td>2</td>
                                <td>125,000,000</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editFinalAccount('FA002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteFinalAccount('FA002')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>FA003</td>
                                <td>إجمالي المصروفات</td>
                                <td>المصروفات</td>
                                <td>مدين</td>
                                <td>3</td>
                                <td>110,000,000</td>
                                <td><span class="status-active">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editFinalAccount('FA003')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteFinalAccount('FA003')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- الأرصدة الافتتاحية -->
                <div id="opening-balancesContent" class="settings-content">
                    <div class="settings-form">
                        <h4>💼 إدخال الأرصدة الافتتاحية</h4>
                        <div class="settings-grid">
                            <div class="settings-field">
                                <label>رقم القيد:</label>
                                <input type="text" id="openingEntryNumber" value="OB2024001" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>رمز الحساب:</label>
                                <select id="openingAccountCode">
                                    <option value="1001">النقدية في الصندوق</option>
                                    <option value="1002">البنك - حساب جاري</option>
                                    <option value="1101">الأثاث والمعدات</option>
                                    <option value="2001">الدائنون</option>
                                    <option value="3001">رأس المال</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>اسم الحساب:</label>
                                <input type="text" id="openingAccountName" value="النقدية في الصندوق" readonly style="background: #f8f9fa;">
                            </div>
                            <div class="settings-field">
                                <label>المبلغ المدين:</label>
                                <input type="number" id="openingDebitAmount" placeholder="المبلغ المدين">
                            </div>
                            <div class="settings-field">
                                <label>المبلغ الدائن:</label>
                                <input type="number" id="openingCreditAmount" placeholder="المبلغ الدائن">
                            </div>
                            <div class="settings-field">
                                <label>تاريخ الرصيد:</label>
                                <input type="date" id="openingBalanceDate" value="2024-01-01">
                            </div>
                            <div class="settings-field">
                                <label>السنة المالية:</label>
                                <select id="openingFiscalYear">
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                </select>
                            </div>
                            <div class="settings-field">
                                <label>حالة الاعتماد:</label>
                                <select id="openingApprovalStatus">
                                    <option value="pending">في الانتظار</option>
                                    <option value="approved">معتمد</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                            <div class="settings-field" style="grid-column: 1 / -1;">
                                <label>البيان:</label>
                                <textarea id="openingDescription" placeholder="بيان الرصيد الافتتاحي"></textarea>
                            </div>
                        </div>
                        <div class="settings-actions">
                            <button class="btn btn-primary" onclick="addOpeningBalance()">➕ إضافة الرصيد</button>
                            <button class="btn btn-secondary" onclick="clearOpeningBalanceForm()">🔄 مسح النموذج</button>
                            <button class="btn btn-primary" onclick="importFromExcel()">📥 استيراد من Excel</button>
                            <button class="btn btn-secondary" onclick="validateBalances()">✅ التحقق من التوازن</button>
                        </div>
                    </div>

                    <!-- ملخص التوازن -->
                    <div class="salary-calculation" style="margin: 20px 0;">
                        <h4>⚖️ ملخص التوازن</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>الجانب المدين</h5>
                                <div class="salary-item">
                                    <span>الأصول الثابتة:</span>
                                    <span id="fixedAssets">45,000,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>الأصول المتداولة:</span>
                                    <span id="currentAssets">25,000,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>المصروفات:</span>
                                    <span id="expenses">5,000,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي المدين:</span>
                                    <span id="totalDebit">75,000,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>الجانب الدائن</h5>
                                <div class="salary-item">
                                    <span>رأس المال:</span>
                                    <span id="capital">50,000,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>الخصوم:</span>
                                    <span id="liabilities">20,000,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>الإيرادات:</span>
                                    <span id="revenues">5,000,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي الدائن:</span>
                                    <span id="totalCredit">75,000,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>حالة التوازن</h5>
                                <div class="salary-item">
                                    <span>الفرق:</span>
                                    <span id="balanceDifference" style="color: #4CAF50;">0</span>
                                </div>
                                <div class="salary-item">
                                    <span>الحالة:</span>
                                    <span id="balanceStatus" style="color: #4CAF50;">متوازن ✅</span>
                                </div>
                                <div class="salary-item">
                                    <span>عدد القيود:</span>
                                    <span id="entriesCount">15</span>
                                </div>
                                <div class="salary-item">
                                    <span>آخر تحديث:</span>
                                    <span id="lastUpdate">2024-01-01</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <table class="settings-table">
                        <thead>
                            <tr>
                                <th>رقم القيد</th>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>المدين</th>
                                <th>الدائن</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="openingBalancesTableBody">
                            <tr>
                                <td>OB2024001</td>
                                <td>1001</td>
                                <td>النقدية في الصندوق</td>
                                <td>5,000,000</td>
                                <td>-</td>
                                <td>2024-01-01</td>
                                <td><span class="status-active">معتمد</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editOpeningBalance('OB2024001')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteOpeningBalance('OB2024001')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>OB2024002</td>
                                <td>1002</td>
                                <td>البنك - حساب جاري</td>
                                <td>20,000,000</td>
                                <td>-</td>
                                <td>2024-01-01</td>
                                <td><span class="status-active">معتمد</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editOpeningBalance('OB2024002')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteOpeningBalance('OB2024002')">حذف</button>
                                </td>
                            </tr>
                            <tr>
                                <td>OB2024003</td>
                                <td>3001</td>
                                <td>رأس المال</td>
                                <td>-</td>
                                <td>25,000,000</td>
                                <td>2024-01-01</td>
                                <td><span class="status-active">معتمد</span></td>
                                <td class="action-buttons">
                                    <button class="btn-edit btn-small" onclick="editOpeningBalance('OB2024003')">تعديل</button>
                                    <button class="btn-delete btn-small" onclick="deleteOpeningBalance('OB2024003')">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- نظام إدارة المستخدمين -->
            <div id="usersSystem" class="users-container">
                <div class="users-header">
                    <h2>👥 نظام إدارة المستخدمين - وزارة الشباب والرياضة</h2>
                    <p>نظام متطور لإدارة المستخدمين والمجموعات مع أمان عالي وصلاحيات ديناميكية</p>
                </div>

                <div class="users-grid">
                    <!-- مجموعة المستخدمين -->
                    <div class="user-section">
                        <div class="section-header">
                            <span class="section-icon">👥</span>
                            <h3 class="section-title">مجموعات المستخدمين</h3>
                        </div>
                        <div class="section-content">
                            <div class="user-form">
                                <h4 style="color: #2c3e50; margin-bottom: 15px;">إضافة مجموعة جديدة</h4>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>رقم المجموعة:</label>
                                        <input type="text" id="groupId" value="GRP001" readonly style="background: #f8f9fa;">
                                    </div>
                                    <div class="input-group">
                                        <label>اسم المجموعة:</label>
                                        <input type="text" id="groupName" placeholder="مثال: المحاسبين">
                                    </div>
                                </div>

                                <div class="form-grid-full">
                                    <div class="input-group">
                                        <label>الملاحظات:</label>
                                        <textarea id="groupNotes" placeholder="وصف المجموعة والصلاحيات..."></textarea>
                                    </div>
                                </div>

                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="addUserGroup()">
                                        ➕ إضافة المجموعة
                                    </button>
                                    <button class="btn btn-secondary" onclick="clearGroupForm()">
                                        🔄 مسح النموذج
                                    </button>
                                </div>
                            </div>

                            <!-- جدول المجموعات -->
                            <table class="users-table">
                                <thead>
                                    <tr>
                                        <th>رقم المجموعة</th>
                                        <th>اسم المجموعة</th>
                                        <th>عدد المستخدمين</th>
                                        <th>الملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="groupsTableBody">
                                    <tr>
                                        <td>GRP001</td>
                                        <td>المدراء</td>
                                        <td>3</td>
                                        <td>صلاحيات كاملة للنظام</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP001')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP001')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>GRP002</td>
                                        <td>المحاسبين</td>
                                        <td>8</td>
                                        <td>صلاحيات المحاسبة والتقارير</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP002')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP002')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>GRP003</td>
                                        <td>موظفي الرواتب</td>
                                        <td>5</td>
                                        <td>صلاحيات نظام الرواتب فقط</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="editGroup('GRP003')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="deleteGroup('GRP003')">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- حسابات المستخدمين -->
                    <div class="user-section">
                        <div class="section-header">
                            <span class="section-icon">🔐</span>
                            <h3 class="section-title">حسابات المستخدمين</h3>
                        </div>
                        <div class="section-content">
                            <div class="user-form">
                                <h4 style="color: #2c3e50; margin-bottom: 15px;">إضافة مستخدم جديد</h4>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>رقم الحساب:</label>
                                        <input type="text" id="userId" value="USR001" readonly style="background: #f8f9fa;">
                                    </div>
                                    <div class="input-group">
                                        <label>اسم الحساب:</label>
                                        <input type="text" id="userFullName" placeholder="الاسم الكامل">
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>اسم المستخدم:</label>
                                        <input type="text" id="username" placeholder="اسم المستخدم للدخول">
                                    </div>
                                    <div class="input-group">
                                        <label>كلمة المرور:</label>
                                        <input type="password" id="userPassword" placeholder="كلمة مرور قوية">
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="input-group">
                                        <label>نوع الحساب:</label>
                                        <select id="userType">
                                            <option value="admin">مدير - كامل الصلاحيات</option>
                                            <option value="user">مستخدم - صلاحيات محدودة</option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label>اسم المجموعة:</label>
                                        <select id="userGroup">
                                            <option value="GRP001">المدراء</option>
                                            <option value="GRP002">المحاسبين</option>
                                            <option value="GRP003">موظفي الرواتب</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-grid-full">
                                    <div class="input-group">
                                        <label>صورة المستخدم:</label>
                                        <div class="file-input-wrapper">
                                            <input type="file" id="userPhoto" class="file-input" accept="image/*" onchange="previewUserPhoto(this)">
                                            <div class="file-input-display">
                                                <span>📷 اختر صورة المستخدم</span>
                                                <img id="userPhotoPreview" class="user-avatar" style="display: none;">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="section-actions">
                                    <button class="btn btn-primary" onclick="addUser()">
                                        ➕ إضافة المستخدم
                                    </button>
                                    <button class="btn btn-secondary" onclick="clearUserForm()">
                                        🔄 مسح النموذج
                                    </button>
                                    <button class="btn btn-danger" onclick="deleteSelectedUser()" id="deleteSelectedBtn" style="display: none;">
                                        🗑️ حذف المستخدم المحدد
                                    </button>
                                </div>
                            </div>

                            <!-- جدول المستخدمين -->
                            <table class="users-table">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>رقم الحساب</th>
                                        <th>اسم الحساب</th>
                                        <th>اسم المستخدم</th>
                                        <th>نوع الحساب</th>
                                        <th>المجموعة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr onclick="selectUserRow(this)" data-user-id="USR001">
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Q0FGNTASCZ8L2NpcmNsZT4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR001</td>
                                        <td>أحمد محمد علي</td>
                                        <td>admin</td>
                                        <td><span class="status-badge status-admin">مدير</span></td>
                                        <td>المدراء</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="event.stopPropagation(); editUser('USR001')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="event.stopPropagation(); deleteUser('USR001')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr onclick="selectUserRow(this)" data-user-id="USR002">
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRjk4MDAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR002</td>
                                        <td>فاطمة حسن محمود</td>
                                        <td>fatima.hassan</td>
                                        <td><span class="status-badge status-user">مستخدم</span></td>
                                        <td>المحاسبين</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="event.stopPropagation(); editUser('USR002')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="event.stopPropagation(); deleteUser('USR002')">حذف</button>
                                        </td>
                                    </tr>
                                    <tr onclick="selectUserRow(this)" data-user-id="USR003">
                                        <td>
                                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM5QzI3QjAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIgMTRDOC42ODYyOSAxNCA2IDE2LjY4NjMgNiAyMFYyMkgxOFYyMEMxOCAxNi42ODYzIDE1LjMxMzcgMTQgMTIgMTRaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
                                                 class="user-avatar" alt="صورة المستخدم">
                                        </td>
                                        <td>USR003</td>
                                        <td>محمد عبدالله أحمد</td>
                                        <td>mohammed.abdullah</td>
                                        <td><span class="status-badge status-user">مستخدم</span></td>
                                        <td>موظفي الرواتب</td>
                                        <td class="action-buttons">
                                            <button class="btn-edit btn-small" onclick="event.stopPropagation(); editUser('USR003')">تعديل</button>
                                            <button class="btn-delete btn-small" onclick="event.stopPropagation(); deleteUser('USR003')">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نظام الرواتب التفاعلي -->
            <div id="payrollSystem" class="payroll-container">
                <div class="payroll-header">
                    <h2>💰 نظام الرواتب - وزارة الشباب والرياضة</h2>
                    <p>نظام متطور لإدارة رواتب الموظفين وفق المعايير الحكومية العراقية</p>
                </div>

                <div class="payroll-tabs">
                    <button class="payroll-tab active" onclick="showPayrollTab('employees')">👥 الموظفون</button>
                    <button class="payroll-tab" onclick="showPayrollTab('positions')">🏢 المناصب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('processing')">⚙️ معالجة الرواتب</button>
                    <button class="payroll-tab" onclick="showPayrollTab('reports')">📊 التقارير</button>
                </div>

                <!-- تبويب الموظفون -->
                <div id="employeesTab" class="tab-content active">
                    <h3>إدارة الموظفين</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>البيانات الشخصية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الرقم الوظيفي:</label>
                                    <input type="text" id="empNumber" value="2024001">
                                </div>
                                <div class="form-group-inline">
                                    <label>رقم الـ IBAN:</label>
                                    <input type="text" id="empIBAN" value="***********************">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الأول:</label>
                                    <input type="text" id="firstName" value="أحمد">
                                </div>
                                <div class="form-group-inline">
                                    <label>الاسم الثاني:</label>
                                    <input type="text" id="middleName" value="محمد">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الاسم الثالث:</label>
                                    <input type="text" id="lastName" value="علي">
                                </div>
                                <div class="form-group-inline">
                                    <label>اسم الأم:</label>
                                    <input type="text" id="motherName" value="فاطمة">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4>البيانات الوظيفية</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>العنوان الوظيفي:</label>
                                    <select id="jobTitle">
                                        <option>محاسب أول</option>
                                        <option>مدير مالي</option>
                                        <option>كاتب حسابات</option>
                                        <option>مراجع حسابات</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>المنصب:</label>
                                    <select id="position" onchange="calculateSalary()">
                                        <option>رئيس قسم</option>
                                        <option>مسؤول شعبة</option>
                                        <option>موظف</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهادة العلمية:</label>
                                    <select id="qualification" onchange="calculateSalary()">
                                        <option>بكالوريوس محاسبة</option>
                                        <option>ماجستير إدارة أعمال</option>
                                        <option>دبلوم عالي</option>
                                        <option>دكتوراه</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدرجة الوظيفية:</label>
                                    <select id="grade" onchange="updateSteps()">
                                        <option value="1">الدرجة الأولى</option>
                                        <option value="2">الدرجة الثانية</option>
                                        <option value="3">الدرجة الثالثة</option>
                                        <option value="4">الدرجة الرابعة</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>المرحلة:</label>
                                    <select id="step" onchange="calculateSalary()">
                                        <option value="1">المرحلة الأولى</option>
                                        <option value="2">المرحلة الثانية</option>
                                        <option value="3">المرحلة الثالثة</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>تاريخ التعيين:</label>
                                    <input type="date" id="hireDate" value="2020-01-15">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="salary-calculation">
                        <h4>💰 حساب الراتب التلقائي</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>الراتب الأساسي والعلاوات</h5>
                                <div class="salary-item">
                                    <span>الراتب الأساسي:</span>
                                    <span id="basicSalary">850,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>العلاوة:</span>
                                    <span id="allowanceAmount">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>فرق راتب:</span>
                                    <span id="salaryDiff">25,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>المجموع:</span>
                                    <span id="basicTotal">950,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المخصصات</h5>
                                <div class="salary-item">
                                    <span>مخصص المنصب:</span>
                                    <span id="positionAllowance">150,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الإعالة:</span>
                                    <span id="familyAllowance">50,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الأولاد:</span>
                                    <span id="childrenAllowance">75,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مخصص الشهادة:</span>
                                    <span id="qualificationAllowance">100,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>المخصص الجامعي:</span>
                                    <span id="universityAllowance">80,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي المخصصات:</span>
                                    <span id="totalAllowances">455,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span id="incomeTax">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين (10%):</span>
                                    <span id="employeeRetirement">140,500</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة (15%):</span>
                                    <span id="deptContribution">210,750</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span id="socialProtection">25,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span id="bankLoans">50,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span id="totalDeductions">566,750</span>
                                </div>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.2); border-radius: 8px;">
                            <div style="font-size: 18px; margin-bottom: 10px;">
                                <strong>إجمالي الراتب: <span id="grossSalary">1,405,000</span> دينار عراقي</strong>
                            </div>
                            <div style="font-size: 20px; font-weight: bold;">
                                <strong>صافي الراتب: <span id="netSalary">838,250</span> دينار عراقي</strong>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-primary" onclick="saveEmployee()">💾 حفظ بيانات الموظف</button>
                        <button class="btn btn-secondary" onclick="calculateSalary()">🧮 إعادة حساب الراتب</button>
                        <button class="btn btn-primary" onclick="generatePayslip()">📄 إنشاء قسيمة راتب</button>
                    </div>

                    <!-- جدول الموظفين -->
                    <h3 style="margin-top: 30px;">قائمة الموظفين</h3>
                    <table class="employees-table">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم الكامل</th>
                                <th>العنوان الوظيفي</th>
                                <th>الدرجة</th>
                                <th>صافي الراتب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024001</td>
                                <td>أحمد محمد علي</td>
                                <td>محاسب أول</td>
                                <td>الدرجة الأولى</td>
                                <td>838,250</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(1)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(1)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024002</td>
                                <td>فاطمة حسن محمود</td>
                                <td>مدير مالي</td>
                                <td>الدرجة الثانية</td>
                                <td>1,250,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(2)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(2)">قسيمة راتب</button>
                                </td>
                            </tr>
                            <tr>
                                <td>2024003</td>
                                <td>محمد عبدالله أحمد</td>
                                <td>كاتب حسابات</td>
                                <td>الدرجة الثالثة</td>
                                <td>650,000</td>
                                <td>
                                    <button class="btn btn-primary" onclick="editEmployee(3)">تعديل</button>
                                    <button class="btn btn-secondary" onclick="viewPayslip(3)">قسيمة راتب</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- تبويب المناصب -->
                <div id="positionsTab" class="tab-content">
                    <h3>إدارة المناصب والدرجات الوظيفية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>المناصب الوظيفية</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>رمز المنصب</th>
                                        <th>اسم المنصب</th>
                                        <th>مخصص المنصب</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>P001</td>
                                        <td>رئيس قسم</td>
                                        <td>150,000</td>
                                        <td>مسؤول عن إدارة القسم</td>
                                    </tr>
                                    <tr>
                                        <td>P002</td>
                                        <td>مسؤول شعبة</td>
                                        <td>100,000</td>
                                        <td>مسؤول عن إدارة الشعبة</td>
                                    </tr>
                                    <tr>
                                        <td>P003</td>
                                        <td>موظف</td>
                                        <td>50,000</td>
                                        <td>موظف عادي</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="form-section">
                            <h4>الدرجات الوظيفية والمراحل</h4>
                            <table class="employees-table">
                                <thead>
                                    <tr>
                                        <th>الدرجة</th>
                                        <th>الراتب الأساسي</th>
                                        <th>عدد المراحل</th>
                                        <th>مبلغ المرحلة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>الدرجة الأولى</td>
                                        <td>850,000</td>
                                        <td>15 مرحلة</td>
                                        <td>25,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثانية</td>
                                        <td>750,000</td>
                                        <td>12 مرحلة</td>
                                        <td>20,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الثالثة</td>
                                        <td>650,000</td>
                                        <td>10 مراحل</td>
                                        <td>15,000</td>
                                    </tr>
                                    <tr>
                                        <td>الدرجة الرابعة</td>
                                        <td>550,000</td>
                                        <td>8 مراحل</td>
                                        <td>12,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- تبويب معالجة الرواتب -->
                <div id="processingTab" class="tab-content">
                    <h3>معالجة الرواتب الشهرية</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات المعالجة</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الشهر:</label>
                                    <select id="payrollMonth">
                                        <option value="12">ديسمبر</option>
                                        <option value="11">نوفمبر</option>
                                        <option value="10">أكتوبر</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>السنة:</label>
                                    <select id="payrollYear">
                                        <option value="2024">2024</option>
                                        <option value="2023">2023</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="processingDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>نوع المعالجة:</label>
                                    <select id="processingType">
                                        <option>معالجة كاملة</option>
                                        <option>معالجة جزئية</option>
                                        <option>إعادة معالجة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="processPayroll()">⚙️ بدء معالجة الرواتب</button>
                        <button class="btn btn-secondary" onclick="validatePayroll()">✅ التحقق من البيانات</button>
                        <button class="btn btn-primary" onclick="approvePayroll()">✔️ اعتماد الرواتب</button>
                    </div>

                    <div class="salary-calculation">
                        <h4>📊 ملخص معالجة الرواتب - ديسمبر 2024</h4>
                        <div class="salary-grid">
                            <div class="salary-section">
                                <h5>إحصائيات عامة</h5>
                                <div class="salary-item">
                                    <span>عدد الموظفين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعالجين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>الموظفين المعتمدين:</span>
                                    <span>156 موظف</span>
                                </div>
                                <div class="salary-item">
                                    <span>حالة المعالجة:</span>
                                    <span style="color: #4CAF50;">مكتملة ✅</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>المبالغ الإجمالية</h5>
                                <div class="salary-item">
                                    <span>إجمالي الرواتب الأساسية:</span>
                                    <span>132,600,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي المخصصات:</span>
                                    <span>70,980,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>إجمالي الاستقطاعات:</span>
                                    <span>88,412,000</span>
                                </div>
                                <div class="salary-total">
                                    <span>صافي الرواتب:</span>
                                    <span>115,168,000</span>
                                </div>
                            </div>

                            <div class="salary-section">
                                <h5>تفاصيل الاستقطاعات</h5>
                                <div class="salary-item">
                                    <span>ضريبة الدخل:</span>
                                    <span>21,918,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>تقاعد الموظفين:</span>
                                    <span>20,358,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>مساهمة الدائرة:</span>
                                    <span>30,537,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>حماية اجتماعية:</span>
                                    <span>3,900,000</span>
                                </div>
                                <div class="salary-item">
                                    <span>قروض مصرفية:</span>
                                    <span>11,699,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب التقارير -->
                <div id="reportsTab" class="tab-content">
                    <h3>تقارير الرواتب</h3>

                    <div class="employee-form">
                        <div class="form-section">
                            <h4>إعدادات التقرير</h4>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>نوع التقرير:</label>
                                    <select id="reportType">
                                        <option>تقرير رواتب شهري</option>
                                        <option>تقرير رواتب حسب القسم</option>
                                        <option>تقرير المخصصات</option>
                                        <option>تقرير الاستقطاعات</option>
                                        <option>قسائم الرواتب</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الفترة:</label>
                                    <select id="reportPeriod">
                                        <option>ديسمبر 2024</option>
                                        <option>نوفمبر 2024</option>
                                        <option>أكتوبر 2024</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group-inline">
                                    <label>تنسيق التصدير:</label>
                                    <select id="exportFormat">
                                        <option>PDF</option>
                                        <option>Excel</option>
                                        <option>Word</option>
                                    </select>
                                </div>
                                <div class="form-group-inline">
                                    <label>الدائرة:</label>
                                    <select id="reportDept">
                                        <option>جميع الدوائر</option>
                                        <option>دائرة الطب الرياضي</option>
                                        <option>دائرة الشؤون الإدارية</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button class="btn btn-primary" onclick="generateReport()">📊 إنشاء التقرير</button>
                        <button class="btn btn-secondary" onclick="previewReport()">👁️ معاينة</button>
                        <button class="btn btn-primary" onclick="exportReport()">📤 تصدير</button>
                    </div>

                    <!-- معاينة التقرير -->
                    <div class="payslip">
                        <div class="payslip-header">
                            <h2>وزارة الشباب والرياضة</h2>
                            <h3>تقرير رواتب شهر ديسمبر 2024</h3>
                            <p>تاريخ التقرير: 2024/12/19</p>
                        </div>

                        <table class="employees-table">
                            <thead>
                                <tr>
                                    <th>ت</th>
                                    <th>الرقم الوظيفي</th>
                                    <th>الاسم الكامل</th>
                                    <th>العنوان الوظيفي</th>
                                    <th>الراتب الأساسي</th>
                                    <th>المخصصات</th>
                                    <th>الاستقطاعات</th>
                                    <th>صافي الراتب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>2024001</td>
                                    <td>أحمد محمد علي</td>
                                    <td>محاسب أول</td>
                                    <td>850,000</td>
                                    <td>455,000</td>
                                    <td>566,750</td>
                                    <td>838,250</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>2024002</td>
                                    <td>فاطمة حسن محمود</td>
                                    <td>مدير مالي</td>
                                    <td>1,200,000</td>
                                    <td>650,000</td>
                                    <td>600,000</td>
                                    <td>1,250,000</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>2024003</td>
                                    <td>محمد عبدالله أحمد</td>
                                    <td>كاتب حسابات</td>
                                    <td>650,000</td>
                                    <td>300,000</td>
                                    <td>300,000</td>
                                    <td>650,000</td>
                                </tr>
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td colspan="4">المجموع الكلي</td>
                                    <td>2,700,000</td>
                                    <td>1,405,000</td>
                                    <td>1,466,750</td>
                                    <td>2,738,250</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-SA');
            const timeStr = now.toLocaleTimeString('ar-SA');

            document.getElementById('currentDate').textContent = dateStr;
            document.getElementById('statusDate').textContent = `التاريخ: ${dateStr}`;
            document.getElementById('statusTime').textContent = `الوقت: ${timeStr}`;
        }

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin123') {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('mainContainer').style.display = 'block';
                updateDateTime();
                setInterval(updateDateTime, 1000);

                // رسالة ترحيب
                setTimeout(() => {
                    alert('تم تسجيل الدخول بنجاح! مرحباً بك في نظام محاسبة وزارة الشباب والرياضة');
                }, 500);
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('password').value = '';
            }
        }

        // عرض الوحدات
        function showModule(moduleName) {
            const modules = {
                'users-list': {
                    title: 'إدارة المستخدمين',
                    content: '',
                    isUsers: true
                },
                'user-groups': {
                    title: 'مجموعات المستخدمين',
                    content: 'هنا يتم إدارة مجموعات المستخدمين مع صلاحيات ديناميكية. الميزات:\n\n• إنشاء مجموعات مستخدمين مخصصة\n• تعيين صلاحيات للمجموعات\n• ربط المستخدمين بالمجموعات\n• وراثة الصلاحيات من المجموعة\n• إدارة هرمية للصلاحيات\n• مجموعات افتراضية: المدراء، المحاسبين، المراجعين'
                },
                'permissions': {
                    title: 'إدارة الصلاحيات',
                    content: 'هنا يتم إدارة صلاحيات النظام بشكل مفصل. النظام يدعم:\n\n• صلاحيات على مستوى الوحدات\n• أنواع الصلاحيات: قراءة، إضافة، تعديل، حذف\n• صلاحيات فردية للمستخدمين\n• صلاحيات جماعية للمجموعات\n• تسجيل تاريخ منح الصلاحيات\n• إمكانية إلغاء وتعديل الصلاحيات'
                },
                'settings-main': {
                    title: 'الإعدادات والتهيئة',
                    content: 'مركز التحكم الرئيسي لإعدادات النظام:\n\n📌 البيانات الأساسية:\n• معلومات المؤسسة والهيكل التنظيمي\n• أدلة الدوائر والأقسام والشعب\n• إعدادات المصارف والصناديق\n• إدارة العملات والفترات المحاسبية\n\n💾 إدارة قواعد البيانات:\n• النسخ الاحتياطي اليدوي والتلقائي\n• استرجاع البيانات والتحقق من السلامة\n• إعدادات الاتصال المحلي والشبكي\n\n🔧 إعدادات متقدمة:\n• الأرصدة الافتتاحية\n• الحسابات الختامية\n• تخصيص واجهة النظام',
                    action: function() {
                        showSettingsSystem();
                    }
                },
                'organization': {
                    title: 'معلومات المؤسسة',
                    content: 'إدارة البيانات الأساسية لوزارة الشباب والرياضة:\n\n📋 البيانات الرسمية:\n• اسم المؤسسة بالعربية والإنجليزية\n• الرقم الرسمي والرمز التعريفي\n• العنوان الكامل والرمز البريدي\n• أرقام الهاتف والفاكس الرسمية\n• البريد الإلكتروني والموقع الرسمي\n\n🖼️ الهوية البصرية:\n• شعار المؤسسة الرسمي\n• الألوان المؤسسية\n• القوالب الرسمية للتقارير\n\n📅 معلومات إضافية:\n• تاريخ التأسيس\n• الوزير المسؤول\n• معلومات الاعتماد والترخيص'
                },
                'departments': {
                    title: 'دليل الدوائر',
                    content: 'إدارة الهيكل التنظيمي للدوائر الرئيسية:\n\n🏢 الدوائر المتاحة:\n• دائرة الطب الرياضي\n• دائرة الشؤون الإدارية والمالية\n• دائرة التخطيط والمتابعة\n• دائرة الأنشطة الرياضية\n• دائرة الشباب والكشافة\n• دائرة الإعلام الرياضي\n• دائرة الاستثمار والمشاريع\n\n📊 إدارة البيانات:\n• رمز الدائرة ووصفها التفصيلي\n• مدير الدائرة ومعلومات الاتصال\n• الموازنة المخصصة والمصروفات\n• ربط الدائرة بالأقسام التابعة\n• تقارير الأداء والإنجازات'
                },
                'sections': {
                    title: 'دليل الأقسام',
                    content: 'إدارة أقسام الدوائر والوحدات التنظيمية:\n\n📁 الأقسام الرئيسية:\n• القسم الإداري والمالي\n• قسم المحاسبة والموازنة\n• قسم الموارد البشرية\n• قسم تقنية المعلومات\n• قسم الخدمات العامة\n• قسم المتابعة والتقييم\n• قسم العلاقات العامة\n\n🔗 الربط التنظيمي:\n• ربط كل قسم بالدائرة الأم\n• تحديد رئيس القسم ونائبه\n• توزيع المهام والمسؤوليات\n• إدارة الصلاحيات والموافقات\n• تقارير الأداء القسمي'
                },
                'divisions': {
                    title: 'دليل الشعب',
                    content: 'إدارة الشعب والوحدات الفرعية داخل الأقسام:\n\n🗂️ الشعب المتخصصة:\n• شعبة الحسابات والمدفوعات\n• شعبة الرواتب والمخصصات\n• شعبة المشتريات والمخازن\n• شعبة الصيانة والخدمات\n• شعبة الأرشيف والوثائق\n• شعبة المتابعة والتدقيق\n\n📋 إدارة التفاصيل:\n• ربط الشعبة بالقسم الأم\n• تحديد مسؤول الشعبة\n• توزيع المهام التفصيلية\n• إدارة الموظفين والاختصاصات\n• تقارير الإنتاجية والكفاءة'
                },
                'banks': {
                    title: 'دليل المصارف',
                    content: 'إدارة شاملة للمصارف والحسابات البنكية:\n\n🏦 المصارف المعتمدة:\n• المصرف العراقي للتجارة (TBI)\n• مصرف الرافدين الحكومي\n• المصرف الأهلي العراقي\n• مصرف بغداد التجاري\n• مصرف الاستثمار العراقي\n\n💳 أنواع الحسابات:\n• حساب التشغيل الرئيسي\n• حساب الرواتب والمخصصات\n• حساب المشاريع الاستثمارية\n• حساب الطوارئ والاحتياطي\n\n📊 إدارة البيانات:\n• أرقام الحسابات وأرقام IBAN\n• أسماء المفوضين بالتوقيع\n• حدود السحب والتحويل\n• تقارير الأرصدة والحركات'
                },
                'bank-branches': {
                    title: 'فروع المصارف',
                    content: 'إدارة فروع المصارف المعتمدة في المحافظات:\n\n🏪 الفروع الرئيسية:\n• فرع بغداد المركزي\n• فرع البصرة الرئيسي\n• فرع أربيل المركزي\n• فرع الموصل الرئيسي\n• فرع النجف الأشرف\n• فرع كربلاء المقدسة\n\n📍 معلومات الفروع:\n• العنوان الكامل ورقم الهاتف\n• مدير الفرع ومعلومات الاتصال\n• ساعات العمل والخدمات المتاحة\n• رموز التحويل والتوجيه\n\n🔗 الربط مع الحسابات:\n• ربط كل فرع بالحسابات المفتوحة\n• إدارة التحويلات بين الفروع\n• تقارير العمليات والمعاملات'
                },
                'currencies': {
                    title: 'دليل العملات',
                    content: 'إدارة العملات وأسعار الصرف المعتمدة:\n\n💱 العملات المدعومة:\n• الدينار العراقي (IQD) - العملة الأساسية\n• الدولار الأمريكي (USD)\n• اليورو الأوروبي (EUR)\n• الجنيه الإسترليني (GBP)\n• الريال السعودي (SAR)\n\n📈 إدارة أسعار الصرف:\n• تحديث يومي لأسعار الصرف\n• أسعار البنك المركزي الرسمية\n• أسعار السوق المحلية\n• تاريخ أسعار الصرف للمراجعة\n\n⚙️ إعدادات العملة:\n• العملة الافتراضية للنظام\n• عدد الخانات العشرية\n• رموز العملات والاختصارات\n• تحويل العملات في التقارير'
                },
                'cashboxes': {
                    title: 'دليل الصناديق',
                    content: 'إدارة صناديق النقد والخزائن المالية:\n\n💰 أنواع الصناديق:\n• صندوق النقدية الرئيسي\n• صناديق الدوائر الفرعية\n• صناديق المشاريع الخاصة\n• صندوق الطوارئ والاحتياطي\n• صناديق العهد والأمانات\n\n🔐 إدارة الأمان:\n• تحديد أمناء الصناديق\n• صلاحيات الإيداع والسحب\n• حدود المبالغ المسموحة\n• نظام التوقيعات المزدوجة\n\n📊 المتابعة والتقارير:\n• تتبع الأرصدة الحالية\n• عمليات الإيداع والسحب اليومية\n• تسوية الصناديق الشهرية\n• تقارير حركة النقد والتدفقات'
                },
                'accounting-periods': {
                    title: 'الفترات المحاسبية',
                    content: 'إدارة الفترات المحاسبية والسنوات المالية:\n\n📅 أنواع الفترات:\n• السنة المالية الحكومية (1 كانون الثاني - 31 كانون الأول)\n• الفترات الشهرية (12 فترة في السنة)\n• الفترات الربعية (4 فترات في السنة)\n• فترات خاصة للمشاريع\n\n⚙️ إعدادات الفترات:\n• تحديد بداية ونهاية كل فترة\n• حالة الفترة (مفتوحة/مغلقة/مؤرشفة)\n• صلاحيات التعديل والإقفال\n• ربط الفترات بالموازنات\n\n🔒 إقفال الفترات:\n• إقفال شهري للحسابات\n• إقفال سنوي وترحيل الأرصدة\n• أرشفة البيانات المقفلة\n• تقارير إقفال الفترات\n\n📊 التقارير المالية:\n• تقارير مقارنة بين الفترات\n• تحليل الأداء المالي\n• مؤشرات النمو والتطور'
                },
                'final-accounts': {
                    title: 'الحسابات الختامية',
                    content: 'إدارة الحسابات الختامية والقوائم المالية:\n\n📊 القوائم المالية الرئيسية:\n• قائمة المركز المالي (الميزانية العمومية)\n• قائمة الدخل والمصروفات\n• قائمة التدفقات النقدية\n• قائمة التغيرات في حقوق الملكية\n• الملاحظات والإيضاحات\n\n🔧 إعدادات الحسابات الختامية:\n• تحديد حسابات الإيرادات والمصروفات\n• حسابات الأصول والخصوم\n• حسابات رأس المال والاحتياطيات\n• قواعد التبويب والتصنيف\n\n⚡ المعالجة التلقائية:\n• إقفال حسابات الإيرادات والمصروفات\n• ترحيل النتائج لحساب الأرباح والخسائر\n• توزيع الأرباح والاحتياطيات\n• إعداد القوائم المالية تلقائياً\n\n📈 التحليل المالي:\n• مؤشرات السيولة والربحية\n• نسب الأداء المالي\n• مقارنات مع السنوات السابقة\n• تقارير تحليلية مفصلة'
                },
                'opening-balances': {
                    title: 'الأرصدة الافتتاحية',
                    content: 'إدخال وإدارة الأرصدة الافتتاحية لبداية السنة المالية:\n\n💼 أنواع الأرصدة:\n• أرصدة الأصول الثابتة والمتداولة\n• أرصدة الخصوم والالتزامات\n• أرصدة حقوق الملكية ورأس المال\n• أرصدة الحسابات الجارية والبنكية\n• أرصدة المخزون والمواد\n\n📝 إدخال الأرصدة:\n• شاشة مخصصة لإدخال الأرصدة\n• استيراد من ملفات Excel\n• التحقق من توازن الميزان\n• مراجعة واعتماد الأرصدة\n\n✅ التحقق والمراجعة:\n• التأكد من توازن المدين والدائن\n• مطابقة مع القوائم المالية السابقة\n• تدقيق الأرصدة من قبل المراجعين\n• اعتماد نهائي من الإدارة المالية\n\n📊 التقارير:\n• تقرير الأرصدة الافتتاحية\n• ميزان المراجعة الافتتاحي\n• مقارنة مع السنة السابقة\n• تقارير الانحرافات والتعديلات'
                },
                'backup-settings': {
                    title: 'إعدادات النسخ الاحتياطي',
                    content: 'إدارة شاملة للنسخ الاحتياطي وحماية البيانات:\n\n💾 أنواع النسخ الاحتياطي:\n• النسخ اليدوي الفوري\n• النسخ التلقائي المجدول\n• النسخ التزايدي (Incremental)\n• النسخ التفاضلي (Differential)\n• النسخ الكامل (Full Backup)\n\n⏰ الجدولة الزمنية:\n• نسخ يومي في نهاية الدوام\n• نسخ أسبوعي يوم الخميس\n• نسخ شهري في نهاية الشهر\n• نسخ سنوي لأرشفة البيانات\n• نسخ طوارئ عند الحاجة\n\n📍 مواقع التخزين:\n• القرص الصلب المحلي\n• خوادم الشبكة المحلية\n• التخزين السحابي الآمن\n• أقراص خارجية محمولة\n• مواقع بديلة للطوارئ\n\n🔐 الأمان والتشفير:\n• تشفير النسخ الاحتياطية\n• كلمات مرور قوية\n• التحقق من سلامة البيانات\n• سجلات العمليات والأخطاء\n\n🔄 الاسترجاع:\n• استرجاع كامل للنظام\n• استرجاع جزئي للبيانات\n• استرجاع نقطة زمنية محددة\n• اختبار سلامة النسخ المسترجعة'
                },
                'database-connection': {
                    title: 'إعدادات قاعدة البيانات',
                    content: 'إدارة اتصالات قاعدة البيانات والشبكة:\n\n🔗 أنواع الاتصال:\n• اتصال محلي (Local Database)\n• اتصال شبكة محلية (LAN)\n• اتصال شبكة واسعة (WAN)\n• اتصال سحابي (Cloud Database)\n• اتصال مختلط (Hybrid)\n\n⚙️ إعدادات SQL Server:\n• اسم الخادم (Server Name)\n• اسم قاعدة البيانات (Database Name)\n• نوع المصادقة (Windows/SQL Authentication)\n• اسم المستخدم وكلمة المرور\n• رقم المنفذ (Port Number)\n• مهلة الاتصال (Connection Timeout)\n\n🛠️ أدوات التكوين:\n• SQL Connection String Builder UI\n• اختبار الاتصال المباشر\n• تشخيص مشاكل الاتصال\n• مراقبة أداء الاتصال\n• سجلات الاتصال والأخطاء\n\n🔒 الأمان والحماية:\n• تشفير البيانات المنقولة (SSL/TLS)\n• جدار الحماية وقواعد الوصول\n• مراقبة محاولات الاتصال\n• تسجيل العمليات الأمنية\n\n📊 المراقبة والأداء:\n• مراقبة استخدام الموارد\n• تحليل أداء الاستعلامات\n• تحسين الفهارس والجداول\n• تقارير الأداء والإحصائيات\n\n🔧 الصيانة:\n• تحديث إحصائيات قاعدة البيانات\n• إعادة بناء الفهارس\n• تنظيف السجلات القديمة\n• ضغط وتحسين قاعدة البيانات'
                },
                'chart-accounts': {
                    title: 'دليل الحسابات',
                    content: 'هنا يتم إدارة شجرة الحسابات المحاسبية وفق النظام الحكومي:\n\n• نوع الاستمارة (تشغيلية/استثمارية)\n• نوع النفقة (جارية/رأسمالية)\n• الفصل والمادة والنوع\n• حسابات تحليلية وإجمالية\n• الأرصدة الافتتاحية والحالية\n• ربط الحسابات الختامية\n• هيكل هرمي متعدد المستويات'
                },
                'journal-entries': {
                    title: 'القيود اليومية',
                    content: 'هنا يتم إدخال وإدارة القيود المحاسبية:\n\n• قيود يومية عادية\n• قيود التسوية\n• قيود الإقفال\n• قيود الافتتاح\n• نظام الموافقات المتدرج\n• ترقيم تلقائي للقيود\n• ربط القيود بالمستندات\n• تدقيق وتوازن القيود'
                },
                'receipt-vouchers': {
                    title: 'سندات القبض',
                    content: 'هنا يتم إدارة سندات القبض:\n\n• سندات قبض نقدية\n• سندات قبض بنكية\n• ربط بالحسابات البنكية\n• ترقيم تلقائي للسندات\n• طباعة السندات\n• تتبع حالة السندات\n• ربط بالقيود المحاسبية\n• تقارير سندات القبض'
                },
                'payment-vouchers': {
                    title: 'سندات الصرف',
                    content: 'هنا يتم إدارة سندات الصرف:\n\n• سندات صرف نقدية\n• سندات صرف بنكية\n• ربط بالحسابات البنكية\n• نظام الموافقات\n• طباعة السندات\n• تتبع المدفوعات\n• ربط بالقيود المحاسبية\n• تقارير سندات الصرف'
                },
                'trial-balance': {
                    title: 'ميزان المراجعة',
                    content: 'هنا يتم عرض ميزان المراجعة:\n\n• ميزان المراجعة الشهري\n• ميزان المراجعة السنوي\n• الأرصدة الافتتاحية\n• حركة الحسابات (مدين/دائن)\n• الأرصدة الختامية\n• التحقق من توازن الميزان\n• تصدير إلى Excel و PDF\n• ميزان مقارن بين الفترات'
                },
                'balance-sheet': {
                    title: 'الميزانية العمومية',
                    content: 'هنا يتم عرض الميزانية العمومية:\n\n• الأصول الثابتة والمتداولة\n• الخصوم والالتزامات\n• حقوق الملكية\n• الميزانية الشهرية والسنوية\n• مقارنة بين السنوات\n• تحليل المؤشرات المالية\n• تصدير التقارير\n• الميزانية المبسطة والمفصلة'
                },
                'employees': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'positions': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'payroll-processing': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'financial-reports': {
                    title: 'التقارير المالية',
                    content: 'هنا يتم عرض التقارير المالية الشاملة:\n\n• تقرير المركز المالي\n• تقرير الدخل والمصروفات\n• تقرير التدفقات النقدية\n• تقرير الأداء المالي\n• تقارير الموازنة والتنفيذ\n• تقارير مقارنة بين الفترات\n• تحليل الانحرافات\n• مؤشرات الأداء المالي\n• تصدير بصيغ متعددة'
                },
                'payroll-reports': {
                    title: 'نظام الرواتب',
                    content: '',
                    isPayroll: true
                },
                'custom-reports': {
                    title: 'التقارير المخصصة',
                    content: 'هنا يتم إنشاء تقارير مخصصة حسب الحاجة:\n\n• منشئ التقارير التفاعلي\n• اختيار الحقول والمعايير\n• تصفية البيانات المتقدمة\n• تجميع وتلخيص البيانات\n• رسوم بيانية وإحصائيات\n• جدولة التقارير التلقائية\n• مشاركة التقارير\n• حفظ قوالب التقارير\n• تصدير بصيغ متعددة'
                }
            };

            const module = modules[moduleName];
            if (module) {
                document.getElementById('modalTitle').textContent = module.title;

                if (module.isUsers) {
                    // إخفاء المحتوى العادي وإظهار نظام إدارة المستخدمين
                    document.getElementById('modalContent').style.display = 'none';
                    document.getElementById('payrollSystem').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'block';
                } else if (module.isPayroll) {
                    // إخفاء المحتوى العادي وإظهار نظام الرواتب
                    document.getElementById('modalContent').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'none';
                    document.getElementById('payrollSystem').style.display = 'block';

                    // تحديد التبويب المناسب
                    if (moduleName === 'employees') {
                        showPayrollTab('employees');
                    } else if (moduleName === 'positions') {
                        showPayrollTab('positions');
                    } else if (moduleName === 'payroll-processing') {
                        showPayrollTab('processing');
                    } else if (moduleName === 'payroll-reports') {
                        showPayrollTab('reports');
                    }
                } else {
                    // إظهار المحتوى العادي وإخفاء الأنظمة الأخرى
                    document.getElementById('modalContent').style.display = 'block';
                    document.getElementById('payrollSystem').style.display = 'none';
                    document.getElementById('usersSystem').style.display = 'none';
                    document.getElementById('settingsSystem').style.display = 'none';

                    // تحويل النص إلى HTML مع تنسيق أفضل
                    const formattedContent = module.content
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/\n/g, '<br>')
                        .replace(/•/g, '<span style="color: #4CAF50; font-weight: bold;">•</span>');

                    document.getElementById('modalContent').innerHTML = `
                        <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                            <p style="line-height: 1.8; font-size: 16px; color: #333; margin: 0;">${formattedContent}</p>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-primary" onclick="alert('هذه الوحدة متاحة في النسخة الكاملة من النظام')">
                                🚀 تفعيل الوحدة
                            </button>
                        </div>
                    `;
                }

                document.getElementById('moduleModal').style.display = 'block';
            }
        }

        // وظائف نظام الرواتب
        function showPayrollTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            const tabButtons = document.querySelectorAll('.payroll-tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName + 'Tab').classList.add('active');

            // تفعيل الزر المناسب
            event.target.classList.add('active');
        }

        function updateSteps() {
            const grade = document.getElementById('grade').value;
            const stepSelect = document.getElementById('step');

            // مسح الخيارات الحالية
            stepSelect.innerHTML = '';

            // إضافة المراحل حسب الدرجة
            const steps = {
                '1': 15, // الدرجة الأولى - 15 مرحلة
                '2': 12, // الدرجة الثانية - 12 مرحلة
                '3': 10, // الدرجة الثالثة - 10 مراحل
                '4': 8   // الدرجة الرابعة - 8 مراحل
            };

            const stepCount = steps[grade] || 10;
            for (let i = 1; i <= stepCount; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `المرحلة ${i}`;
                stepSelect.appendChild(option);
            }

            // إعادة حساب الراتب
            calculateSalary();
        }

        function calculateSalary() {
            // الحصول على القيم
            const grade = parseInt(document.getElementById('grade').value);
            const step = parseInt(document.getElementById('step').value) || 1;
            const position = document.getElementById('position').value;
            const qualification = document.getElementById('qualification').value;

            // الرواتب الأساسية حسب الدرجة
            const basicSalaries = {
                1: 850000,
                2: 750000,
                3: 650000,
                4: 550000
            };

            // مبالغ المراحل
            const stepAmounts = {
                1: 25000,
                2: 20000,
                3: 15000,
                4: 12000
            };

            // حساب الراتب الأساسي
            const basicSalary = basicSalaries[grade] || 650000;
            const allowance = 75000; // علاوة ثابتة
            const salaryDiff = (step - 1) * (stepAmounts[grade] || 15000);
            const basicTotal = basicSalary + allowance + salaryDiff;

            // حساب المخصصات
            const positionAllowances = {
                'رئيس قسم': 150000,
                'مسؤول شعبة': 100000,
                'موظف': 50000
            };

            const qualificationAllowances = {
                'دكتوراه': 150000,
                'ماجستير إدارة أعمال': 120000,
                'بكالوريوس محاسبة': 100000,
                'دبلوم عالي': 80000
            };

            const positionAllowance = positionAllowances[position] || 50000;
            const familyAllowance = 50000;
            const childrenAllowance = 75000;
            const qualificationAllowance = qualificationAllowances[qualification] || 100000;
            const universityAllowance = 80000;

            const totalAllowances = positionAllowance + familyAllowance + childrenAllowance +
                                  qualificationAllowance + universityAllowance;

            // حساب إجمالي الراتب
            const grossSalary = basicTotal + totalAllowances;

            // حساب الاستقطاعات
            const incomeTax = grossSalary * 0.10; // 10% ضريبة دخل
            const employeeRetirement = grossSalary * 0.10; // 10% تقاعد موظفين
            const deptContribution = grossSalary * 0.15; // 15% مساهمة دائرة
            const socialProtection = 25000;
            const bankLoans = 50000;

            const totalDeductions = incomeTax + employeeRetirement + deptContribution +
                                  socialProtection + bankLoans;

            // حساب صافي الراتب
            const netSalary = grossSalary - totalDeductions;

            // تحديث العرض
            document.getElementById('basicSalary').textContent = basicSalary.toLocaleString();
            document.getElementById('allowanceAmount').textContent = allowance.toLocaleString();
            document.getElementById('salaryDiff').textContent = salaryDiff.toLocaleString();
            document.getElementById('basicTotal').textContent = basicTotal.toLocaleString();

            document.getElementById('positionAllowance').textContent = positionAllowance.toLocaleString();
            document.getElementById('familyAllowance').textContent = familyAllowance.toLocaleString();
            document.getElementById('childrenAllowance').textContent = childrenAllowance.toLocaleString();
            document.getElementById('qualificationAllowance').textContent = qualificationAllowance.toLocaleString();
            document.getElementById('universityAllowance').textContent = universityAllowance.toLocaleString();
            document.getElementById('totalAllowances').textContent = totalAllowances.toLocaleString();

            document.getElementById('incomeTax').textContent = Math.round(incomeTax).toLocaleString();
            document.getElementById('employeeRetirement').textContent = Math.round(employeeRetirement).toLocaleString();
            document.getElementById('deptContribution').textContent = Math.round(deptContribution).toLocaleString();
            document.getElementById('socialProtection').textContent = socialProtection.toLocaleString();
            document.getElementById('bankLoans').textContent = bankLoans.toLocaleString();
            document.getElementById('totalDeductions').textContent = Math.round(totalDeductions).toLocaleString();

            document.getElementById('grossSalary').textContent = grossSalary.toLocaleString();
            document.getElementById('netSalary').textContent = Math.round(netSalary).toLocaleString();
        }

        function saveEmployee() {
            const firstName = document.getElementById('firstName').value;
            const middleName = document.getElementById('middleName').value;
            const lastName = document.getElementById('lastName').value;

            alert(`تم حفظ بيانات الموظف: ${firstName} ${middleName} ${lastName} بنجاح!`);
        }

        function generatePayslip() {
            alert('تم إنشاء قسيمة الراتب بنجاح! سيتم تحميل الملف قريباً...');
        }

        function editEmployee(id) {
            alert(`تحرير بيانات الموظف رقم: ${id}`);
        }

        function viewPayslip(id) {
            alert(`عرض قسيمة راتب الموظف رقم: ${id}`);
        }

        function processPayroll() {
            const month = document.getElementById('payrollMonth').value;
            const year = document.getElementById('payrollYear').value;

            alert(`تم بدء معالجة رواتب شهر ${month}/${year} بنجاح!`);
        }

        function validatePayroll() {
            alert('تم التحقق من صحة البيانات بنجاح! جميع البيانات صحيحة ✅');
        }

        function approvePayroll() {
            alert('تم اعتماد الرواتب بنجاح! ✅');
        }

        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            alert(`تم إنشاء ${reportType} بنجاح!`);
        }

        function previewReport() {
            alert('معاينة التقرير متاحة أدناه 👁️');
        }

        function exportReport() {
            const format = document.getElementById('exportFormat').value;
            alert(`تم تصدير التقرير بصيغة ${format} بنجاح! 📤`);
        }

        // وظائف نظام إدارة المستخدمين
        let groupCounter = 4;
        let userCounter = 4;
        let selectedUserId = null;

        function addUserGroup() {
            const groupName = document.getElementById('groupName').value;
            const groupNotes = document.getElementById('groupNotes').value;

            if (!groupName.trim()) {
                alert('يرجى إدخال اسم المجموعة');
                return;
            }

            const newGroupId = `GRP${String(groupCounter).padStart(3, '0')}`;
            const tableBody = document.getElementById('groupsTableBody');

            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${newGroupId}</td>
                <td>${groupName}</td>
                <td>0</td>
                <td>${groupNotes || 'لا توجد ملاحظات'}</td>
                <td class="action-buttons">
                    <button class="btn-edit btn-small" onclick="editGroup('${newGroupId}')">تعديل</button>
                    <button class="btn-delete btn-small" onclick="deleteGroup('${newGroupId}')">حذف</button>
                </td>
            `;

            tableBody.appendChild(newRow);

            // تحديث قائمة المجموعات في نموذج المستخدمين
            const userGroupSelect = document.getElementById('userGroup');
            const newOption = document.createElement('option');
            newOption.value = newGroupId;
            newOption.textContent = groupName;
            userGroupSelect.appendChild(newOption);

            groupCounter++;
            document.getElementById('groupId').value = `GRP${String(groupCounter).padStart(3, '0')}`;

            clearGroupForm();
            alert(`تم إضافة المجموعة "${groupName}" بنجاح! ✅`);
        }

        function clearGroupForm() {
            document.getElementById('groupName').value = '';
            document.getElementById('groupNotes').value = '';
        }

        function editGroup(groupId) {
            alert(`تحرير المجموعة: ${groupId}`);
        }

        function deleteGroup(groupId) {
            if (confirm(`هل تريد حذف المجموعة ${groupId}؟`)) {
                // حذف الصف من الجدول
                const rows = document.querySelectorAll('#groupsTableBody tr');
                rows.forEach(row => {
                    if (row.cells[0].textContent === groupId) {
                        row.remove();
                    }
                });

                // حذف من قائمة المجموعات في نموذج المستخدمين
                const userGroupSelect = document.getElementById('userGroup');
                const options = userGroupSelect.querySelectorAll('option');
                options.forEach(option => {
                    if (option.value === groupId) {
                        option.remove();
                    }
                });

                alert(`تم حذف المجموعة ${groupId} بنجاح! ✅`);
            }
        }

        function addUser() {
            const userFullName = document.getElementById('userFullName').value;
            const username = document.getElementById('username').value;
            const userPassword = document.getElementById('userPassword').value;
            const userType = document.getElementById('userType').value;
            const userGroup = document.getElementById('userGroup').value;

            if (!userFullName.trim() || !username.trim() || !userPassword.trim()) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            const newUserId = `USR${String(userCounter).padStart(3, '0')}`;
            const tableBody = document.getElementById('usersTableBody');

            // إنشاء صورة افتراضية
            const avatarColors = ['#4CAF50', '#FF9800', '#9C27B0', '#2196F3', '#F44336'];
            const randomColor = avatarColors[Math.floor(Math.random() * avatarColors.length)];
            const avatarSvg = `data:image/svg+xml;base64,${btoa(`
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="20" fill="${randomColor}"/>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z" fill="white"/>
                        <path d="M12 14C8.68629 14 6 16.6863 6 20V22H18V20C18 16.6863 15.3137 14 12 14Z" fill="white"/>
                    </svg>
                </svg>
            `)}`;

            const userTypeText = userType === 'admin' ? 'مدير' : 'مستخدم';
            const statusClass = userType === 'admin' ? 'status-admin' : 'status-user';

            // الحصول على اسم المجموعة
            const groupSelect = document.getElementById('userGroup');
            const groupName = groupSelect.options[groupSelect.selectedIndex].text;

            const newRow = document.createElement('tr');
            newRow.setAttribute('onclick', `selectUserRow(this)`);
            newRow.setAttribute('data-user-id', newUserId);
            newRow.innerHTML = `
                <td>
                    <img src="${avatarSvg}" class="user-avatar" alt="صورة المستخدم">
                </td>
                <td>${newUserId}</td>
                <td>${userFullName}</td>
                <td>${username}</td>
                <td><span class="status-badge ${statusClass}">${userTypeText}</span></td>
                <td>${groupName}</td>
                <td class="action-buttons">
                    <button class="btn-edit btn-small" onclick="event.stopPropagation(); editUser('${newUserId}')">تعديل</button>
                    <button class="btn-delete btn-small" onclick="event.stopPropagation(); deleteUser('${newUserId}')">حذف</button>
                </td>
            `;

            tableBody.appendChild(newRow);

            userCounter++;
            document.getElementById('userId').value = `USR${String(userCounter).padStart(3, '0')}`;

            clearUserForm();
            alert(`تم إضافة المستخدم "${userFullName}" بنجاح! ✅`);
        }

        function clearUserForm() {
            document.getElementById('userFullName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('userPassword').value = '';
            document.getElementById('userType').selectedIndex = 0;
            document.getElementById('userGroup').selectedIndex = 0;

            // إخفاء معاينة الصورة
            const preview = document.getElementById('userPhotoPreview');
            preview.style.display = 'none';
            document.querySelector('.file-input-display span').textContent = '📷 اختر صورة المستخدم';
        }

        function editUser(userId) {
            alert(`تحرير المستخدم: ${userId}`);
        }

        function previewUserPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('userPhotoPreview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    document.querySelector('.file-input-display span').textContent = '✅ تم اختيار الصورة';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // وظائف التحديد والحذف
        function selectUserRow(row) {
            // إزالة التحديد من جميع الصفوف
            const allRows = document.querySelectorAll('#usersTableBody tr');
            allRows.forEach(r => r.classList.remove('selected'));

            // تحديد الصف الحالي
            row.classList.add('selected');
            selectedUserId = row.getAttribute('data-user-id');

            // إظهار زر الحذف
            const deleteBtn = document.getElementById('deleteSelectedBtn');
            deleteBtn.style.display = 'inline-block';

            // تحديث نص الزر ليشمل اسم المستخدم
            const userName = row.cells[2].textContent; // اسم الحساب
            deleteBtn.innerHTML = `🗑️ حذف المستخدم: ${userName}`;
        }

        function deleteSelectedUser() {
            if (!selectedUserId) {
                alert('يرجى تحديد مستخدم للحذف');
                return;
            }

            const selectedRow = document.querySelector(`tr[data-user-id="${selectedUserId}"]`);
            const userName = selectedRow.cells[2].textContent;

            if (confirm(`هل تريد حذف المستخدم "${userName}" نهائياً؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!`)) {
                // حذف الصف من الجدول
                selectedRow.remove();

                // إخفاء زر الحذف
                const deleteBtn = document.getElementById('deleteSelectedBtn');
                deleteBtn.style.display = 'none';

                // إعادة تعيين المتغير
                selectedUserId = null;

                alert(`تم حذف المستخدم "${userName}" بنجاح! ✅`);
            }
        }

        // تحديث وظيفة الحذف الفردي لتتعامل مع التحديد
        function deleteUser(userId) {
            const row = document.querySelector(`tr[data-user-id="${userId}"]`);
            const userName = row.cells[2].textContent;

            if (confirm(`هل تريد حذف المستخدم "${userName}"؟`)) {
                row.remove();

                // إذا كان هذا المستخدم محدد، إخفاء زر الحذف
                if (selectedUserId === userId) {
                    document.getElementById('deleteSelectedBtn').style.display = 'none';
                    selectedUserId = null;
                }

                alert(`تم حذف المستخدم "${userName}" بنجاح! ✅`);
            }
        }

        // دالة لإخفاء جميع الأنظمة
        function hideAllSystems() {
            document.getElementById('payrollSystem').style.display = 'none';
            document.getElementById('usersSystem').style.display = 'none';
            document.getElementById('settingsSystem').style.display = 'none';
        }

        // دالة لإظهار نظام إدارة المستخدمين
        function showUsersSystem() {
            hideAllSystems();
            document.getElementById('usersSystem').style.display = 'block';
            document.getElementById('modalTitle').textContent = 'نظام إدارة المستخدمين';
            document.getElementById('modalContent').style.display = 'none';
        }

        // دالة لإظهار نظام الإعدادات
        function showSettingsSystem() {
            hideAllSystems();
            document.getElementById('settingsSystem').style.display = 'block';
            document.getElementById('modalTitle').textContent = 'نظام الإعدادات والتهيئة';
            document.getElementById('modalContent').style.display = 'none';
        }

        // دالة لإظهار تبويب الإعدادات
        function showSettingsTab(tabName) {
            // إخفاء جميع المحتويات
            const contents = document.querySelectorAll('.settings-content');
            contents.forEach(content => {
                content.classList.remove('active');
            });

            // إزالة الفئة النشطة من جميع التبويبات
            const tabs = document.querySelectorAll('.settings-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // إظهار المحتوى المحدد
            const targetContent = document.getElementById(tabName + 'Content');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // تفعيل التبويب المحدد
            event.target.classList.add('active');
        }

        // دوال حفظ البيانات (غير مفعلة)
        function saveOrganizationInfo() {
            alert('تم حفظ معلومات المؤسسة بنجاح! 💾\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function resetOrganizationForm() {
            if (confirm('هل تريد إعادة تعيين جميع البيانات؟')) {
                document.getElementById('orgNameAr').value = 'وزارة الشباب والرياضة';
                document.getElementById('orgNameEn').value = 'Ministry of Youth and Sports';
                alert('تم إعادة تعيين النموذج بنجاح! 🔄');
            }
        }

        function addDepartment() {
            alert('تم إضافة الدائرة بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearDepartmentForm() {
            document.getElementById('deptName').value = '';
            document.getElementById('deptManager').value = '';
            document.getElementById('deptPhone').value = '';
            document.getElementById('deptBudget').value = '';
            document.getElementById('deptDescription').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editDepartment(deptCode) {
            alert('فتح نموذج تعديل الدائرة: ' + deptCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteDepartment(deptCode) {
            if (confirm('هل تريد حذف الدائرة: ' + deptCode + '؟')) {
                alert('تم حذف الدائرة بنجاح! 🗑️');
            }
        }

        function addSection() {
            alert('تم إضافة القسم بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearSectionForm() {
            document.getElementById('sectionName').value = '';
            document.getElementById('sectionHead').value = '';
            document.getElementById('sectionDeputy').value = '';
            document.getElementById('sectionTasks').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editSection(sectionCode) {
            alert('فتح نموذج تعديل القسم: ' + sectionCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteSection(sectionCode) {
            if (confirm('هل تريد حذف القسم: ' + sectionCode + '؟')) {
                alert('تم حذف القسم بنجاح! 🗑️');
            }
        }

        function addDivision() {
            alert('تم إضافة الشعبة بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearDivisionForm() {
            document.getElementById('divisionName').value = '';
            document.getElementById('divisionHead').value = '';
            document.getElementById('divisionEmployees').value = '';
            document.getElementById('divisionSpecialty').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editDivision(divisionCode) {
            alert('فتح نموذج تعديل الشعبة: ' + divisionCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteDivision(divisionCode) {
            if (confirm('هل تريد حذف الشعبة: ' + divisionCode + '؟')) {
                alert('تم حذف الشعبة بنجاح! 🗑️');
            }
        }

        function addBank() {
            alert('تم إضافة المصرف بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearBankForm() {
            document.getElementById('bankName').value = '';
            document.getElementById('bankAbbr').value = '';
            document.getElementById('bankOperatingAccount').value = '';
            document.getElementById('bankPayrollAccount').value = '';
            document.getElementById('bankOperatingIBAN').value = '';
            document.getElementById('bankPayrollIBAN').value = '';
            document.getElementById('bankAuthorizedSignatory').value = '';
            document.getElementById('bankDailyLimit').value = '';
            document.getElementById('bankPhone').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editBank(bankCode) {
            alert('فتح نموذج تعديل المصرف: ' + bankCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteBank(bankCode) {
            if (confirm('هل تريد حذف المصرف: ' + bankCode + '؟')) {
                alert('تم حذف المصرف بنجاح! 🗑️');
            }
        }

        function addBranch() {
            alert('تم إضافة الفرع بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearBranchForm() {
            document.getElementById('branchName').value = '';
            document.getElementById('branchManager').value = '';
            document.getElementById('branchPhone').value = '';
            document.getElementById('branchSwiftCode').value = '';
            document.getElementById('branchAddress').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editBranch(branchCode) {
            alert('فتح نموذج تعديل الفرع: ' + branchCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteBranch(branchCode) {
            if (confirm('هل تريد حذف الفرع: ' + branchCode + '؟')) {
                alert('تم حذف الفرع بنجاح! 🗑️');
            }
        }

        function addCashbox() {
            alert('تم إضافة الصندوق بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearCashboxForm() {
            document.getElementById('cashboxName').value = '';
            document.getElementById('cashboxCustodian').value = '';
            document.getElementById('cashboxOpeningBalance').value = '';
            document.getElementById('cashboxMaxLimit').value = '';
            document.getElementById('cashboxNotes').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function editCashbox(cashboxCode) {
            alert('فتح نموذج تعديل الصندوق: ' + cashboxCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteCashbox(cashboxCode) {
            if (confirm('هل تريد حذف الصندوق: ' + cashboxCode + '؟')) {
                alert('تم حذف الصندوق بنجاح! 🗑️');
            }
        }

        function addCurrency() {
            alert('تم إضافة العملة بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearCurrencyForm() {
            document.getElementById('currencyCode').value = '';
            document.getElementById('currencyNameAr').value = '';
            document.getElementById('currencyNameEn').value = '';
            document.getElementById('currencySymbol').value = '';
            document.getElementById('currencyExchangeRate').value = '';
            document.getElementById('currencyNotes').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function updateExchangeRates() {
            alert('تم تحديث أسعار الصرف بنجاح! 📈\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function editCurrency(currencyCode) {
            alert('فتح نموذج تعديل العملة: ' + currencyCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteCurrency(currencyCode) {
            if (confirm('هل تريد حذف العملة: ' + currencyCode + '؟')) {
                alert('تم حذف العملة بنجاح! 🗑️');
            }
        }

        function addPeriod() {
            alert('تم إضافة الفترة المحاسبية بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearPeriodForm() {
            document.getElementById('periodName').value = '';
            document.getElementById('periodDescription').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function closePeriod() {
            if (confirm('هل تريد إقفال الفترة المحاسبية؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!')) {
                alert('تم إقفال الفترة المحاسبية بنجاح! 🔒');
            }
        }

        function editPeriod(periodCode) {
            alert('فتح نموذج تعديل الفترة: ' + periodCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deletePeriod(periodCode) {
            if (confirm('هل تريد حذف الفترة: ' + periodCode + '؟')) {
                alert('تم حذف الفترة بنجاح! 🗑️');
            }
        }

        function addFinalAccount() {
            alert('تم إضافة الحساب الختامي بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearFinalAccountForm() {
            document.getElementById('finalAccountName').value = '';
            document.getElementById('finalAccountOrder').value = '';
            document.getElementById('finalAccountDescription').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function generateFinalStatements() {
            alert('تم إنشاء القوائم المالية بنجاح! 📊\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function editFinalAccount(accountCode) {
            alert('فتح نموذج تعديل الحساب: ' + accountCode + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteFinalAccount(accountCode) {
            if (confirm('هل تريد حذف الحساب: ' + accountCode + '؟')) {
                alert('تم حذف الحساب بنجاح! 🗑️');
            }
        }

        function addOpeningBalance() {
            alert('تم إضافة الرصيد الافتتاحي بنجاح! ➕\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function clearOpeningBalanceForm() {
            document.getElementById('openingDebitAmount').value = '';
            document.getElementById('openingCreditAmount').value = '';
            document.getElementById('openingDescription').value = '';
            alert('تم مسح النموذج بنجاح! 🔄');
        }

        function importFromExcel() {
            alert('تم استيراد البيانات من Excel بنجاح! 📥\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function validateBalances() {
            alert('تم التحقق من التوازن بنجاح! ✅\n\nالنتيجة: الميزان متوازن\nالمدين = الدائن = 75,000,000 دينار');
        }

        function editOpeningBalance(entryNumber) {
            alert('فتح نموذج تعديل القيد: ' + entryNumber + '\n\nملاحظة: هذه واجهة تجريبية فقط');
        }

        function deleteOpeningBalance(entryNumber) {
            if (confirm('هل تريد حذف القيد: ' + entryNumber + '؟')) {
                alert('تم حذف القيد بنجاح! 🗑️');
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('moduleModal').style.display = 'none';
        }

        // عرض معلومات البرنامج
        function showAbout() {
            alert('نظام محاسبة وزارة الشباب والرياضة\nالإصدار 1.0.0\nتم التطوير باستخدام VB.NET\nجميع الحقوق محفوظة © 2024');
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('moduleModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // تحديث التاريخ والوقت عند تحميل الصفحة
        updateDateTime();

        // تحديث حسابات الراتب عند تحميل الصفحة
        setTimeout(() => {
            if (document.getElementById('basicSalary')) {
                calculateSalary();
            }
        }, 1000);
    </script>
</body>
</html>
