<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاسبة وزارة الشباب والرياضة - عرض توضيحي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .title {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: bold;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-btn:hover {
            background: #45a049;
        }

        .default-credentials {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }

        .default-credentials h4 {
            color: #2196F3;
            margin-bottom: 10px;
        }

        .main-container {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .nav-menu {
            background: #34495e;
            padding: 0;
            overflow-x: auto;
        }

        .nav-menu ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-menu li {
            position: relative;
        }

        .nav-menu a {
            display: block;
            color: white;
            padding: 15px 20px;
            text-decoration: none;
            transition: background 0.3s;
            white-space: nowrap;
        }

        .nav-menu a:hover {
            background: #2c3e50;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: #2c3e50;
            min-width: 200px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            display: none;
            z-index: 1000;
        }

        .nav-menu li:hover .dropdown {
            display: block;
        }

        .content {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .welcome-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        .welcome-card h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 28px;
        }

        .welcome-card p {
            color: #666;
            font-size: 18px;
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-bottom: 15px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .status-bar {
            background: #ecf0f1;
            padding: 10px 20px;
            border-top: 1px solid #bdc3c7;
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #666;
        }

        @media (max-width: 768px) {
            .nav-menu ul {
                flex-direction: column;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- نموذج تسجيل الدخول -->
    <div id="loginContainer" class="login-container">
        <div class="login-form">
            <div class="logo">وش</div>
            <h2 class="title">نظام محاسبة وزارة الشباب والرياضة</h2>
            <p class="subtitle">نظام محاسبة ورواتب متطور وذكي</p>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" required>
                </div>
                
                <button type="submit" class="login-btn">تسجيل الدخول</button>
            </form>
            
            <div class="default-credentials">
                <h4>بيانات تسجيل الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
        </div>
    </div>

    <!-- النموذج الرئيسي -->
    <div id="mainContainer" class="main-container">
        <!-- رأس الصفحة -->
        <header class="header">
            <h1>نظام محاسبة وزارة الشباب والرياضة</h1>
            <div class="user-info">
                <span id="currentUser">المستخدم: admin</span>
                <span id="currentDate"></span>
                <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
            </div>
        </header>

        <!-- شريط التنقل -->
        <nav class="nav-menu">
            <ul>
                <li>
                    <a href="#" onclick="showModule('users')">إدارة المستخدمين</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('users-list')">المستخدمون</a>
                        <a href="#" onclick="showModule('user-groups')">مجموعات المستخدمين</a>
                        <a href="#" onclick="showModule('permissions')">الصلاحيات</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('settings')">الإعدادات</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('organization')">معلومات المؤسسة</a>
                        <a href="#" onclick="showModule('departments')">الدوائر</a>
                        <a href="#" onclick="showModule('sections')">الأقسام</a>
                        <a href="#" onclick="showModule('banks')">المصارف</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('accounting')">المحاسبة</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('chart-accounts')">دليل الحسابات</a>
                        <a href="#" onclick="showModule('journal-entries')">القيود اليومية</a>
                        <a href="#" onclick="showModule('receipt-vouchers')">سندات القبض</a>
                        <a href="#" onclick="showModule('payment-vouchers')">سندات الصرف</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('payroll')">الرواتب</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('employees')">الموظفون</a>
                        <a href="#" onclick="showModule('positions')">المناصب</a>
                        <a href="#" onclick="showModule('payroll-processing')">معالجة الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showModule('reports')">التقارير</a>
                    <div class="dropdown">
                        <a href="#" onclick="showModule('financial-reports')">التقارير المالية</a>
                        <a href="#" onclick="showModule('payroll-reports')">تقارير الرواتب</a>
                    </div>
                </li>
                <li>
                    <a href="#" onclick="showAbout()">حول البرنامج</a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="content">
            <div class="welcome-card">
                <h2>مرحباً بك في نظام محاسبة وزارة الشباب والرياضة</h2>
                <p>نظام محاسبة ورواتب متطور وذكي مصمم خصيصاً لوزارة الشباب والرياضة</p>
                <p>تم تطويره باستخدام أحدث التقنيات البرمجية والمحاسبية الحديثة</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>نظام شامل لإدارة المستخدمين والصلاحيات مع تشفير آمن وصلاحيات ديناميكية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <h3>الإعدادات والتهيئة</h3>
                    <p>إدارة الهيكل التنظيمي والمصارف والعملات مع نظام النسخ الاحتياطي التلقائي</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>النظام المحاسبي</h3>
                    <p>شجرة حسابات متعددة المستويات مع القيود اليومية والتقارير المحاسبية الشاملة</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3>نظام الرواتب</h3>
                    <p>حساب تلقائي للرواتب مع جميع المخصصات والاستقطاعات وفق المعايير الحكومية</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>التقارير والتحليلات</h3>
                    <p>تقارير مالية ومحاسبية شاملة مع إمكانية التصدير إلى PDF و Excel</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3>دعم اللغة العربية</h3>
                    <p>واجهة RTL كاملة مع دعم الخطوط العربية والتواريخ الهجرية والميلادية</p>
                </div>
            </div>
        </main>

        <!-- شريط الحالة -->
        <div class="status-bar">
            <span>جاهز</span>
            <span id="statusUser">المستخدم: admin</span>
            <span id="statusDate"></span>
            <span id="statusTime"></span>
        </div>
    </div>

    <!-- نافذة منبثقة للوحدات -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">عنوان الوحدة</h2>
            <p id="modalContent">محتوى الوحدة</p>
        </div>
    </div>

    <script>
        // تحديث التاريخ والوقت
        function updateDateTime() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('ar-SA');
            const timeStr = now.toLocaleTimeString('ar-SA');
            
            document.getElementById('currentDate').textContent = dateStr;
            document.getElementById('statusDate').textContent = `التاريخ: ${dateStr}`;
            document.getElementById('statusTime').textContent = `الوقت: ${timeStr}`;
        }

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'admin123') {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('mainContainer').style.display = 'block';
                updateDateTime();
                setInterval(updateDateTime, 1000);
                
                // رسالة ترحيب
                setTimeout(() => {
                    alert('تم تسجيل الدخول بنجاح! مرحباً بك في نظام محاسبة وزارة الشباب والرياضة');
                }, 500);
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        });

        // تسجيل الخروج
        function logout() {
            if (confirm('هل تريد تسجيل الخروج من النظام؟')) {
                document.getElementById('mainContainer').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('password').value = '';
            }
        }

        // عرض الوحدات
        function showModule(moduleName) {
            const modules = {
                'users-list': {
                    title: 'إدارة المستخدمين',
                    content: 'هنا يتم إدارة المستخدمين وصلاحياتهم مع تشفير آمن وحماية من الهجمات'
                },
                'user-groups': {
                    title: 'مجموعات المستخدمين',
                    content: 'هنا يتم إدارة مجموعات المستخدمين مع صلاحيات ديناميكية'
                },
                'permissions': {
                    title: 'الصلاحيات',
                    content: 'هنا يتم إدارة صلاحيات النظام (قراءة، إضافة، تعديل، حذف)'
                },
                'organization': {
                    title: 'معلومات المؤسسة',
                    content: 'هنا يتم إدارة معلومات وزارة الشباب والرياضة'
                },
                'departments': {
                    title: 'الدوائر',
                    content: 'هنا يتم إدارة دوائر الوزارة مثل دائرة الطب الرياضي'
                },
                'sections': {
                    title: 'الأقسام',
                    content: 'هنا يتم إدارة أقسام الدوائر مثل القسم الإداري'
                },
                'banks': {
                    title: 'المصارف',
                    content: 'هنا يتم إدارة المصارف وفروعها مع حسابات التشغيل والرواتب'
                },
                'chart-accounts': {
                    title: 'دليل الحسابات',
                    content: 'هنا يتم إدارة شجرة الحسابات المحاسبية متعددة المستويات'
                },
                'journal-entries': {
                    title: 'القيود اليومية',
                    content: 'هنا يتم إدخال وإدارة القيود المحاسبية مع نظام الموافقات'
                },
                'receipt-vouchers': {
                    title: 'سندات القبض',
                    content: 'هنا يتم إدارة سندات القبض مع ربط الحسابات البنكية'
                },
                'payment-vouchers': {
                    title: 'سندات الصرف',
                    content: 'هنا يتم إدارة سندات الصرف مع ربط الحسابات البنكية'
                },
                'employees': {
                    title: 'الموظفون',
                    content: 'هنا يتم إدارة بيانات الموظفين مع المناصب والدرجات الوظيفية'
                },
                'positions': {
                    title: 'المناصب',
                    content: 'هنا يتم إدارة المناصب والعناوين الوظيفية والشهادات العلمية'
                },
                'payroll-processing': {
                    title: 'معالجة الرواتب',
                    content: 'هنا يتم حساب ومعالجة رواتب الموظفين مع جميع المخصصات والاستقطاعات'
                },
                'financial-reports': {
                    title: 'التقارير المالية',
                    content: 'هنا يتم عرض التقارير المالية: ميزان المراجعة، الميزانية العمومية، قائمة الدخل'
                },
                'payroll-reports': {
                    title: 'تقارير الرواتب',
                    content: 'هنا يتم عرض تقارير الرواتب وقسائم الرواتب والمخصصات'
                }
            };

            const module = modules[moduleName];
            if (module) {
                document.getElementById('modalTitle').textContent = module.title;
                document.getElementById('modalContent').textContent = module.content;
                document.getElementById('moduleModal').style.display = 'block';
            }
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('moduleModal').style.display = 'none';
        }

        // عرض معلومات البرنامج
        function showAbout() {
            alert('نظام محاسبة وزارة الشباب والرياضة\nالإصدار 1.0.0\nتم التطوير باستخدام VB.NET\nجميع الحقوق محفوظة © 2024');
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('moduleModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // تحديث التاريخ والوقت عند تحميل الصفحة
        updateDateTime();
    </script>
</body>
</html>
