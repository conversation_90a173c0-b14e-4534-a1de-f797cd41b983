# نظام محاسبة وزارة الشباب والرياضة
## Ministry of Youth and Sports Accounting System

### 📋 نظرة عامة
نظام محاسبة ورواتب احترافي وذكي مصمم خصيصاً لوزارة الشباب والرياضة، مطور باستخدام VB.NET مع أحدث التقنيات البرمجية والمحاسبية الحديثة.

### 🎯 الميزات الرئيسية

#### 🔐 إدارة المستخدمين والصلاحيات
- إدارة شاملة للمستخدمين مع تشفير كلمات المرور
- نظام مجموعات المستخدمين مع صلاحيات ديناميكية
- صلاحيات مفصلة (قراءة، إضافة، تعديل، حذف) لكل وحدة
- تسجيل دخول آمن مع حماية من الهجمات

#### ⚙️ الإعدادات والتهيئة
- إدارة معلومات المؤسسة والهيكل التنظيمي
- دليل الدوائر والأقسام والشعب
- إدارة المصارف وفروعها مع حسابات التشغيل والرواتب
- إدارة الصناديق والعملات
- نظام النسخ الاحتياطي التلقائي والاسترجاع

#### 📊 النظام المحاسبي
- شجرة حسابات ديناميكية متعددة المستويات
- نظام القيود اليومية المتقدم
- سندات القبض والصرف
- التقارير المحاسبية الشاملة:
  - ميزان المراجعة
  - الميزانية العمومية
  - قائمة الدخل
  - كشف الحسابات التفصيلي

#### 👥 نظام الرواتب
- إدارة شاملة لبيانات الموظفين
- نظام المناصب والعناوين الوظيفية
- إدارة الشهادات العلمية والدرجات الوظيفية
- نظام المراحل (أكثر من 10 مراحل)
- حساب تلقائي للرواتب مع جميع المخصصات والاستقطاعات:
  - **المخصصات**: المنصب، الإعالة، الأولاد، الهندسية، الشهادة، الحرفة، الخطورة، الموقع الجغرافي، الجامعية
  - **الاستقطاعات**: ضريبة الدخل، تقاعد الموظفين (10%)، مساهمة الدائرة (15%)، حماية اجتماعية، دوائر تنفيذ، قروض مصرفية

### 🛠️ التقنيات المستخدمة

#### البرمجة والتطوير
- **لغة البرمجة**: VB.NET Framework 4.8
- **قاعدة البيانات**: SQL Server مع Entity Framework 6.4.4
- **واجهة المستخدم**: DevExpress Controls v23.1
- **الأمان**: تشفير SHA256 لكلمات المرور
- **التقارير**: DevExpress Reports مع دعم PDF و Excel

#### المعمارية
- **نمط التصميم**: MVP (Model-View-Presenter)
- **طبقات النظام**: 
  - Data Access Layer (DAL)
  - Business Logic Layer (BLL)
  - Presentation Layer (UI)
- **البرمجة الكائنية**: OOP مع Repository Pattern

### 📁 هيكل المشروع

```
MinistryAccountingSystem/
├── Models/                     # نماذج البيانات
│   ├── UserManagement.vb      # نماذج إدارة المستخدمين
│   ├── Settings.vb            # نماذج الإعدادات
│   ├── Accounting.vb          # نماذج المحاسبة
│   └── Payroll.vb             # نماذج الرواتب
├── DataAccess/                # طبقة الوصول للبيانات
│   └── MinistryDbContext.vb   # Entity Framework Context
├── Services/                  # طبقة الخدمات
│   ├── BaseService.vb         # الخدمة الأساسية
│   └── UserManagementService.vb # خدمة إدارة المستخدمين
├── Forms/                     # النماذج والواجهات
│   ├── MainForm.vb            # النموذج الرئيسي
│   └── LoginForm.vb           # نموذج تسجيل الدخول
├── Scripts/                   # سكريبتات قاعدة البيانات
│   └── CreateDatabase.sql     # سكريبت إنشاء قاعدة البيانات
└── My Project/                # ملفات المشروع
```

### 🚀 متطلبات التشغيل

#### متطلبات النظام
- **نظام التشغيل**: Windows 7 أو أحدث
- **إطار العمل**: .NET Framework 4.8
- **قاعدة البيانات**: SQL Server 2012 أو أحدث
- **الذاكرة**: 4 GB RAM كحد أدنى
- **مساحة القرص**: 500 MB للتطبيق + مساحة إضافية لقاعدة البيانات

#### المتطلبات الإضافية
- DevExpress Controls v23.1 أو أحدث
- SQL Server Management Studio (اختياري)

### 📦 التثبيت والإعداد

#### 1. إعداد قاعدة البيانات
```sql
-- تشغيل سكريبت إنشاء قاعدة البيانات
sqlcmd -S .\SQLEXPRESS -i "Scripts\CreateDatabase.sql"
```

#### 2. تكوين سلسلة الاتصال
تحديث ملف `App.config` مع معلومات قاعدة البيانات:
```xml
<connectionStrings>
    <add name="MinistryAccountingDB" 
         connectionString="Data Source=YOUR_SERVER;Initial Catalog=MinistryAccountingDB;Integrated Security=True" 
         providerName="System.Data.SqlClient" />
</connectionStrings>
```

#### 3. بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### 🌐 دعم اللغة العربية

النظام مصمم بالكامل لدعم اللغة العربية مع:
- واجهة مستخدم RTL (من اليمين إلى اليسار)
- دعم الخطوط العربية
- تقارير باللغة العربية
- تواريخ هجرية وميلادية

### 📊 التقارير المتاحة

#### التقارير المحاسبية
- اليومية العامة
- كشف حساب تفصيلي
- تقرير أرصدة الحسابات
- ميزان المراجعة
- الميزانية العمومية
- قائمة الدخل

#### تقارير الرواتب
- قسيمة راتب فردية
- تقرير رواتب شهري
- تقرير رواتب حسب القسم
- تقرير المخصصات والاستقطاعات
- تقرير إجمالي الرواتب

### 🔒 الأمان والحماية

- تشفير كلمات المرور باستخدام SHA256
- نظام صلاحيات متقدم على مستوى الوحدات
- حماية من SQL Injection
- تسجيل جميع العمليات (Audit Trail)
- نظام النسخ الاحتياطي التلقائي

### 🔧 الصيانة والدعم

#### النسخ الاحتياطي
- نسخ احتياطي تلقائي يومي
- إمكانية النسخ الاحتياطي اليدوي
- استرجاع النسخ مع التحقق من سلامة البيانات

#### السجلات
- سجلات التطبيق: `C:\MinistryAccounting\Logs\application.log`
- سجلات الأخطاء: `C:\MinistryAccounting\Logs\errors.log`
- سجلات الخدمات: `C:\MinistryAccounting\Logs\service_*.log`

### 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +964-1-XXXXXXX

### 📄 الترخيص

هذا النظام مطور خصيصاً لوزارة الشباب والرياضة ومحمي بحقوق الطبع والنشر.

### 🔄 التحديثات المستقبلية

#### الميزات المخططة
- تكامل مع الذكاء الاصطناعي للتحليل التنبؤي
- تطبيق ويب للوصول عن بُعد
- تكامل مع الأنظمة الحكومية الأخرى
- تقارير تفاعلية متقدمة
- نظام الموافقات الإلكترونية

---

**تم تطوير هذا النظام باستخدام أحدث المعايير البرمجية والمحاسبية لضمان الكفاءة والموثوقية.**
