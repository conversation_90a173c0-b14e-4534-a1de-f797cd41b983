# دليل التثبيت والإعداد
## Installation and Setup Guide

### 📋 متطلبات النظام

#### الحد الأدنى من المتطلبات
- **نظام التشغيل**: Windows 7 SP1 أو أحدث (يُفضل Windows 10/11)
- **المعالج**: Intel Core i3 أو AMD equivalent
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 2 GB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى
- **الشبكة**: اتصال بالإنترنت للتحديثات (اختياري)

#### المتطلبات الموصى بها
- **نظام التشغيل**: Windows 10/11 Pro
- **المعالج**: Intel Core i5 أو أعلى
- **الذاكرة**: 8 GB RAM أو أكثر
- **مساحة القرص**: 5 GB مساحة فارغة
- **الشاشة**: دقة 1920x1080 أو أعلى

### 🛠️ المتطلبات البرمجية

#### المتطلبات الأساسية
1. **.NET Framework 4.8**
   - تحميل من: https://dotnet.microsoft.com/download/dotnet-framework/net48
   - مطلوب لتشغيل التطبيق

2. **SQL Server 2012 أو أحدث**
   - SQL Server Express (مجاني): https://www.microsoft.com/sql-server/sql-server-downloads
   - أو SQL Server Developer Edition
   - أو SQL Server Standard/Enterprise

3. **Visual C++ Redistributable**
   - مطلوب لبعض مكونات DevExpress
   - تحميل من موقع Microsoft

#### المتطلبات الاختيارية
1. **SQL Server Management Studio (SSMS)**
   - لإدارة قاعدة البيانات
   - تحميل من: https://docs.microsoft.com/sql/ssms/download-sql-server-management-studio-ssms

2. **Adobe Acrobat Reader**
   - لعرض التقارير PDF
   - تحميل من: https://get.adobe.com/reader/

### 📦 خطوات التثبيت

#### الخطوة 1: إعداد قاعدة البيانات

1. **تثبيت SQL Server**
   ```bash
   # تشغيل مثبت SQL Server Express
   SQLServer2019-SSEI-Expr.exe
   ```

2. **إنشاء قاعدة البيانات**
   ```sql
   -- فتح SQL Server Management Studio
   -- الاتصال بالخادم المحلي: .\SQLEXPRESS
   -- تشغيل سكريبت إنشاء قاعدة البيانات
   ```

3. **تشغيل سكريبت الإنشاء**
   ```cmd
   sqlcmd -S .\SQLEXPRESS -i "Scripts\CreateDatabase.sql"
   ```

#### الخطوة 2: تكوين التطبيق

1. **تحديث ملف App.config**
   ```xml
   <connectionStrings>
     <add name="MinistryAccountingDB" 
          connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=MinistryAccountingDB;Integrated Security=True;MultipleActiveResultSets=True" 
          providerName="System.Data.SqlClient" />
   </connectionStrings>
   ```

2. **إنشاء المجلدات المطلوبة**
   ```cmd
   mkdir "C:\MinistryAccounting\Backups"
   mkdir "C:\MinistryAccounting\Reports"
   mkdir "C:\MinistryAccounting\Temp"
   mkdir "C:\MinistryAccounting\Logs"
   mkdir "C:\MinistryAccounting\Database"
   ```

3. **تعيين الصلاحيات**
   - منح صلاحيات الكتابة للمجلدات المنشأة
   - تأكد من أن المستخدم له صلاحيات الوصول لقاعدة البيانات

#### الخطوة 3: تشغيل التطبيق

1. **التشغيل الأول**
   ```cmd
   MinistryAccountingSystem.exe
   ```

2. **بيانات تسجيل الدخول الافتراضية**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

3. **تغيير كلمة المرور**
   - يُنصح بتغيير كلمة المرور الافتراضية فوراً

### ⚙️ الإعدادات المتقدمة

#### إعداد النسخ الاحتياطي التلقائي

1. **تكوين مهمة Windows**
   ```cmd
   # إنشاء مهمة مجدولة للنسخ الاحتياطي
   schtasks /create /tn "Ministry Backup" /tr "C:\MinistryAccounting\Backup.bat" /sc daily /st 02:00
   ```

2. **إنشاء ملف Backup.bat**
   ```batch
   @echo off
   sqlcmd -S .\SQLEXPRESS -Q "BACKUP DATABASE MinistryAccountingDB TO DISK = 'C:\MinistryAccounting\Backups\MinistryAccountingDB_%date:~-4,4%%date:~-10,2%%date:~-7,2%.bak'"
   ```

#### إعداد الشبكة (للاستخدام المتعدد)

1. **تكوين SQL Server للشبكة**
   ```sql
   -- تمكين TCP/IP في SQL Server Configuration Manager
   -- إعادة تشغيل خدمة SQL Server
   ```

2. **تحديث سلسلة الاتصال**
   ```xml
   <add name="MinistryAccountingDB" 
        connectionString="Data Source=SERVER_IP\SQLEXPRESS;Initial Catalog=MinistryAccountingDB;User ID=sa;Password=your_password" 
        providerName="System.Data.SqlClient" />
   ```

3. **فتح منافذ الجدار الناري**
   ```cmd
   netsh advfirewall firewall add rule name="SQL Server" dir=in action=allow protocol=TCP localport=1433
   ```

### 🔧 استكشاف الأخطاء وإصلاحها

#### مشاكل شائعة وحلولها

1. **خطأ في الاتصال بقاعدة البيانات**
   ```
   الخطأ: "Cannot connect to database"
   الحل: 
   - تأكد من تشغيل خدمة SQL Server
   - تحقق من سلسلة الاتصال في App.config
   - تأكد من وجود قاعدة البيانات
   ```

2. **خطأ في الصلاحيات**
   ```
   الخطأ: "Access denied"
   الحل:
   - تشغيل التطبيق كمدير
   - تحقق من صلاحيات المجلدات
   - تأكد من صلاحيات قاعدة البيانات
   ```

3. **خطأ في DevExpress**
   ```
   الخطأ: "DevExpress components not found"
   الحل:
   - تثبيت DevExpress Runtime
   - تحديث مراجع المشروع
   - إعادة تجميع التطبيق
   ```

4. **مشاكل الخطوط العربية**
   ```
   الخطأ: "Arabic text not displaying correctly"
   الحل:
   - تثبيت خطوط عربية إضافية
   - تحديث إعدادات النظام للغة العربية
   - تأكد من تفعيل RTL في النظام
   ```

#### ملفات السجلات

1. **مواقع ملفات السجلات**
   ```
   C:\MinistryAccounting\Logs\application.log    - سجل التطبيق العام
   C:\MinistryAccounting\Logs\errors.log         - سجل الأخطاء
   C:\MinistryAccounting\Logs\security.log       - سجل الأمان
   C:\MinistryAccounting\Logs\service_*.log      - سجلات الخدمات
   ```

2. **مراقبة الأداء**
   ```cmd
   # عرض استخدام الذاكرة
   tasklist /fi "imagename eq MinistryAccountingSystem.exe"
   
   # مراقبة قاعدة البيانات
   sqlcmd -S .\SQLEXPRESS -Q "SELECT * FROM sys.dm_exec_sessions WHERE program_name LIKE '%Ministry%'"
   ```

### 🔄 التحديثات والصيانة

#### تحديث التطبيق

1. **النسخ الاحتياطي قبل التحديث**
   ```sql
   BACKUP DATABASE MinistryAccountingDB TO DISK = 'C:\MinistryAccounting\Backups\PreUpdate_Backup.bak'
   ```

2. **تطبيق التحديث**
   ```cmd
   # إيقاف التطبيق
   # نسخ الملفات الجديدة
   # تشغيل سكريبت تحديث قاعدة البيانات (إن وجد)
   # إعادة تشغيل التطبيق
   ```

#### الصيانة الدورية

1. **تنظيف قاعدة البيانات**
   ```sql
   -- إعادة فهرسة الجداول
   EXEC sp_recompile 'TableName'
   
   -- تحديث الإحصائيات
   UPDATE STATISTICS TableName
   ```

2. **تنظيف الملفات المؤقتة**
   ```cmd
   # حذف التقارير القديمة (أكثر من 30 يوم)
   forfiles /p "C:\MinistryAccounting\Reports" /s /m *.* /d -30 /c "cmd /c del @path"
   
   # حذف ملفات السجلات القديمة
   forfiles /p "C:\MinistryAccounting\Logs" /s /m *.log /d -90 /c "cmd /c del @path"
   ```

### 📞 الدعم الفني

#### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964-1-XXXXXXX
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 4:00 م

#### معلومات مطلوبة عند طلب الدعم
1. رقم إصدار التطبيق
2. نظام التشغيل المستخدم
3. رسالة الخطأ الكاملة
4. خطوات إعادة إنتاج المشكلة
5. ملفات السجلات ذات الصلة

---

**ملاحظة**: يُرجى قراءة هذا الدليل بعناية قبل البدء في التثبيت. في حالة مواجهة أي مشاكل، يُرجى الرجوع إلى قسم استكشاف الأخطاء أو الاتصال بالدعم الفني.
