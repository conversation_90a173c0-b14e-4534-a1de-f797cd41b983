Imports Microsoft.VisualBasic.ApplicationServices
Imports System.Globalization
Imports System.Threading

Namespace My
    ''' <summary>
    ''' أحداث التطبيق الرئيسية لنظام محاسبة وزارة الشباب والرياضة
    ''' </summary>
    Partial Friend Class MyApplication

        ''' <summary>
        ''' يتم تنفيذه عند بدء تشغيل التطبيق
        ''' </summary>
        Private Sub MyApplication_Startup(sender As Object, e As StartupEventArgs) Handles Me.Startup
            Try
                ' تعيين الثقافة العربية كافتراضية
                Dim arabicCulture As New CultureInfo("ar-SA")
                Thread.CurrentThread.CurrentCulture = arabicCulture
                Thread.CurrentThread.CurrentUICulture = arabicCulture

                ' تعيين اتجاه النص من اليمين إلى اليسار
                Application.SetCompatibleTextRenderingDefault(False)
                
                ' إنشاء المجلدات المطلوبة إذا لم تكن موجودة
                CreateRequiredDirectories()
                
                ' تسجيل بدء تشغيل التطبيق
                LogApplicationStart()
                
            Catch ex As Exception
                MessageBox.Show($"خطأ في بدء تشغيل التطبيق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End Sub

        ''' <summary>
        ''' يتم تنفيذه عند إغلاق التطبيق
        ''' </summary>
        Private Sub MyApplication_Shutdown(sender As Object, e As EventArgs) Handles Me.Shutdown
            Try
                ' تسجيل إغلاق التطبيق
                LogApplicationShutdown()
                
                ' حفظ الإعدادات
                My.Settings.Save()
                
            Catch ex As Exception
                ' تجاهل الأخطاء عند الإغلاق
            End Try
        End Sub

        ''' <summary>
        ''' إنشاء المجلدات المطلوبة للتطبيق
        ''' </summary>
        Private Sub CreateRequiredDirectories()
            Try
                Dim directories() As String = {
                    My.Settings.BackupPath,
                    My.Settings.ReportsPath,
                    My.Settings.TempPath,
                    "C:\MinistryAccounting\Logs\"
                }

                For Each directory As String In directories
                    If Not String.IsNullOrEmpty(directory) AndAlso Not IO.Directory.Exists(directory) Then
                        IO.Directory.CreateDirectory(directory)
                    End If
                Next
            Catch ex As Exception
                ' تجاهل أخطاء إنشاء المجلدات
            End Try
        End Sub

        ''' <summary>
        ''' تسجيل بدء تشغيل التطبيق
        ''' </summary>
        Private Sub LogApplicationStart()
            Try
                Dim logPath As String = "C:\MinistryAccounting\Logs\application.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - تم بدء تشغيل نظام محاسبة وزارة الشباب والرياضة{Environment.NewLine}"
                IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8)
            Catch ex As Exception
                ' تجاهل أخطاء التسجيل
            End Try
        End Sub

        ''' <summary>
        ''' تسجيل إغلاق التطبيق
        ''' </summary>
        Private Sub LogApplicationShutdown()
            Try
                Dim logPath As String = "C:\MinistryAccounting\Logs\application.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - تم إغلاق نظام محاسبة وزارة الشباب والرياضة{Environment.NewLine}"
                IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8)
            Catch ex As Exception
                ' تجاهل أخطاء التسجيل
            End Try
        End Sub

        ''' <summary>
        ''' معالجة الاستثناءات غير المعالجة
        ''' </summary>
        Private Sub MyApplication_UnhandledException(sender As Object, e As UnhandledExceptionEventArgs) Handles Me.UnhandledException
            Try
                Dim errorMessage As String = $"حدث خطأ غير متوقع: {e.Exception.Message}{Environment.NewLine}{e.Exception.StackTrace}"
                
                ' تسجيل الخطأ
                Dim logPath As String = "C:\MinistryAccounting\Logs\errors.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - خطأ غير معالج: {errorMessage}{Environment.NewLine}"
                IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8)
                
                ' عرض رسالة للمستخدم
                MessageBox.Show("حدث خطأ غير متوقع في التطبيق. يرجى مراجعة ملف السجلات للحصول على تفاصيل أكثر.", 
                              "خطأ في النظام", MessageBoxButtons.OK, MessageBoxIcon.Error)
                
            Catch
                ' تجاهل أخطاء معالجة الأخطاء
            End Try
        End Sub

    End Class
End Namespace
