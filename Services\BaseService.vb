Imports System.Data.Entity
Imports MinistryAccountingSystem.DataAccess

Namespace Services

    ''' <summary>
    ''' الخدمة الأساسية التي ترث منها جميع الخدمات الأخرى
    ''' </summary>
    Public MustInherit Class BaseService
        Implements IDisposable

        Protected ReadOnly _context As MinistryDbContext
        Private _disposed As Boolean = False

        ''' <summary>
        ''' منشئ الخدمة الأساسية
        ''' </summary>
        Public Sub New()
            _context = New MinistryDbContext()
        End Sub

        ''' <summary>
        ''' منشئ الخدمة الأساسية مع سياق مخصص
        ''' </summary>
        ''' <param name="context">سياق قاعدة البيانات</param>
        Public Sub New(context As MinistryDbContext)
            _context = context
        End Sub

        ''' <summary>
        ''' حفظ التغييرات في قاعدة البيانات
        ''' </summary>
        ''' <returns>عدد السجلات المتأثرة</returns>
        Protected Function SaveChanges() As Integer
            Try
                Return _context.SaveChanges()
            Catch ex As Exception
                LogError("خطأ في حفظ التغييرات", ex)
                Throw
            End Try
        End Function

        ''' <summary>
        ''' حفظ التغييرات بشكل غير متزامن
        ''' </summary>
        ''' <returns>عدد السجلات المتأثرة</returns>
        Protected Async Function SaveChangesAsync() As Task(Of Integer)
            Try
                Return Await _context.SaveChangesAsync()
            Catch ex As Exception
                LogError("خطأ في حفظ التغييرات (غير متزامن)", ex)
                Throw
            End Try
        End Function

        ''' <summary>
        ''' بدء معاملة قاعدة بيانات
        ''' </summary>
        ''' <returns>معاملة قاعدة البيانات</returns>
        Protected Function BeginTransaction() As DbContextTransaction
            Return _context.Database.BeginTransaction()
        End Function

        ''' <summary>
        ''' تسجيل الأخطاء
        ''' </summary>
        ''' <param name="message">رسالة الخطأ</param>
        ''' <param name="exception">الاستثناء</param>
        Protected Sub LogError(message As String, exception As Exception)
            Try
                Dim logPath As String = "C:\MinistryAccounting\Logs\service_errors.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}: {exception.Message}{Environment.NewLine}{exception.StackTrace}{Environment.NewLine}"
                IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8)
            Catch
                ' تجاهل أخطاء التسجيل
            End Try
        End Sub

        ''' <summary>
        ''' تسجيل المعلومات
        ''' </summary>
        ''' <param name="message">رسالة المعلومات</param>
        Protected Sub LogInfo(message As String)
            Try
                Dim logPath As String = "C:\MinistryAccounting\Logs\service_info.log"
                Dim logEntry As String = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}"
                IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8)
            Catch
                ' تجاهل أخطاء التسجيل
            End Try
        End Sub

        ''' <summary>
        ''' التحقق من صحة البيانات
        ''' </summary>
        ''' <param name="entity">الكائن المراد التحقق منه</param>
        ''' <returns>قائمة بأخطاء التحقق</returns>
        Protected Function ValidateEntity(entity As Object) As List(Of String)
            Dim errors As New List(Of String)
            
            If entity Is Nothing Then
                errors.Add("الكائن فارغ")
                Return errors
            End If

            ' يمكن إضافة قواعد تحقق مخصصة هنا
            ' مثل التحقق من الحقول المطلوبة، طول النصوص، إلخ

            Return errors
        End Function

        ''' <summary>
        ''' التحقق من وجود سجل بناءً على معرف
        ''' </summary>
        ''' <typeparam name="T">نوع الكائن</typeparam>
        ''' <param name="dbSet">مجموعة البيانات</param>
        ''' <param name="id">المعرف</param>
        ''' <returns>true إذا كان السجل موجود</returns>
        Protected Function RecordExists(Of T As Class)(dbSet As DbSet(Of T), id As Integer) As Boolean
            Try
                Return dbSet.Find(id) IsNot Nothing
            Catch ex As Exception
                LogError($"خطأ في التحقق من وجود السجل {GetType(T).Name}", ex)
                Return False
            End Try
        End Function

        ''' <summary>
        ''' الحصول على السجل التالي في التسلسل
        ''' </summary>
        ''' <param name="tableName">اسم الجدول</param>
        ''' <param name="columnName">اسم العمود</param>
        ''' <returns>الرقم التالي</returns>
        Protected Function GetNextSequenceNumber(tableName As String, columnName As String) As Integer
            Try
                Dim sql As String = $"SELECT ISNULL(MAX({columnName}), 0) + 1 FROM {tableName}"
                Return _context.Database.SqlQuery(Of Integer)(sql).FirstOrDefault()
            Catch ex As Exception
                LogError($"خطأ في الحصول على الرقم التالي للجدول {tableName}", ex)
                Return 1
            End Try
        End Function

        ''' <summary>
        ''' تنظيف الموارد
        ''' </summary>
        Public Sub Dispose() Implements IDisposable.Dispose
            Dispose(True)
            GC.SuppressFinalize(Me)
        End Sub

        ''' <summary>
        ''' تنظيف الموارد المحمية
        ''' </summary>
        ''' <param name="disposing">هل يتم التنظيف</param>
        Protected Overridable Sub Dispose(disposing As Boolean)
            If Not _disposed Then
                If disposing Then
                    _context?.Dispose()
                End If
                _disposed = True
            End If
        End Sub

        ''' <summary>
        ''' المدمر
        ''' </summary>
        Protected Overrides Sub Finalize()
            Dispose(False)
            MyBase.Finalize()
        End Sub

    End Class

    ''' <summary>
    ''' نتيجة العملية
    ''' </summary>
    Public Class ServiceResult
        Public Property IsSuccess As Boolean
        Public Property Message As String
        Public Property Data As Object
        Public Property Errors As List(Of String)

        Public Sub New()
            Errors = New List(Of String)
        End Sub

        Public Shared Function Success(Optional message As String = "تمت العملية بنجاح", Optional data As Object = Nothing) As ServiceResult
            Return New ServiceResult With {
                .IsSuccess = True,
                .Message = message,
                .Data = data
            }
        End Function

        Public Shared Function Failure(message As String, Optional errors As List(Of String) = Nothing) As ServiceResult
            Return New ServiceResult With {
                .IsSuccess = False,
                .Message = message,
                .Errors = If(errors, New List(Of String))
            }
        End Function
    End Class

    ''' <summary>
    ''' نتيجة العملية مع نوع محدد
    ''' </summary>
    ''' <typeparam name="T">نوع البيانات</typeparam>
    Public Class ServiceResult(Of T)
        Public Property IsSuccess As Boolean
        Public Property Message As String
        Public Property Data As T
        Public Property Errors As List(Of String)

        Public Sub New()
            Errors = New List(Of String)
        End Sub

        Public Shared Function Success(data As T, Optional message As String = "تمت العملية بنجاح") As ServiceResult(Of T)
            Return New ServiceResult(Of T) With {
                .IsSuccess = True,
                .Message = message,
                .Data = data
            }
        End Function

        Public Shared Function Failure(message As String, Optional errors As List(Of String) = Nothing) As ServiceResult(Of T)
            Return New ServiceResult(Of T) With {
                .IsSuccess = False,
                .Message = message,
                .Errors = If(errors, New List(Of String))
            }
        End Function
    End Class

End Namespace
