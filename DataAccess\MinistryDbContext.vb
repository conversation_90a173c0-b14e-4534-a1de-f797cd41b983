Imports System.Data.Entity
Imports System.Data.Entity.ModelConfiguration.Conventions
Imports MinistryAccountingSystem.Models.UserManagement
Imports MinistryAccountingSystem.Models.Settings
Imports MinistryAccountingSystem.Models.Accounting
Imports MinistryAccountingSystem.Models.Payroll

Namespace DataAccess

    ''' <summary>
    ''' سياق قاعدة البيانات الرئيسي لنظام محاسبة وزارة الشباب والرياضة
    ''' </summary>
    Public Class MinistryDbContext
        Inherits DbContext

        ''' <summary>
        ''' منشئ السياق
        ''' </summary>
        Public Sub New()
            MyBase.New("name=MinistryAccountingDB")
            
            ' تعطيل التهيئة التلقائية لقاعدة البيانات
            Database.SetInitializer(Of MinistryDbContext)(Nothing)
            
            ' تمكين Lazy Loading
            Configuration.LazyLoadingEnabled = True
            Configuration.ProxyCreationEnabled = True
        End Sub

        ''' <summary>
        ''' منشئ السياق مع سلسلة الاتصال
        ''' </summary>
        ''' <param name="connectionString">سلسلة الاتصال</param>
        Public Sub New(connectionString As String)
            MyBase.New(connectionString)
            Database.SetInitializer(Of MinistryDbContext)(Nothing)
            Configuration.LazyLoadingEnabled = True
            Configuration.ProxyCreationEnabled = True
        End Sub

        #Region "User Management DbSets"
        Public Property Users As DbSet(Of User)
        Public Property UserGroups As DbSet(Of UserGroup)
        Public Property Permissions As DbSet(Of Permission)
        Public Property UserPermissions As DbSet(Of UserPermission)
        Public Property GroupPermissions As DbSet(Of GroupPermission)
        #End Region

        #Region "Settings DbSets"
        Public Property OrganizationInfo As DbSet(Of OrganizationInfo)
        Public Property Departments As DbSet(Of Department)
        Public Property Sections As DbSet(Of Section)
        Public Property Divisions As DbSet(Of Division)
        Public Property Banks As DbSet(Of Bank)
        Public Property BankBranches As DbSet(Of BankBranch)
        Public Property CashBoxes As DbSet(Of CashBox)
        Public Property Currencies As DbSet(Of Currency)
        #End Region

        #Region "Accounting DbSets"
        Public Property ChartOfAccounts As DbSet(Of ChartOfAccount)
        Public Property JournalEntryTypes As DbSet(Of JournalEntryType)
        Public Property JournalEntries As DbSet(Of JournalEntry)
        Public Property JournalEntryDetails As DbSet(Of JournalEntryDetail)
        Public Property ReceiptVouchers As DbSet(Of ReceiptVoucher)
        Public Property PaymentVouchers As DbSet(Of PaymentVoucher)
        #End Region

        #Region "Payroll DbSets"
        Public Property Positions As DbSet(Of Position)
        Public Property JobTitles As DbSet(Of JobTitle)
        Public Property Qualifications As DbSet(Of Qualification)
        Public Property JobGrades As DbSet(Of JobGrade)
        Public Property JobSteps As DbSet(Of JobStep)
        Public Property Employees As DbSet(Of Employee)
        Public Property AllowanceTypes As DbSet(Of AllowanceType)
        Public Property EmployeeAllowances As DbSet(Of EmployeeAllowance)
        Public Property DeductionTypes As DbSet(Of DeductionType)
        Public Property EmployeeDeductions As DbSet(Of EmployeeDeduction)
        Public Property PayrollRecords As DbSet(Of PayrollRecord)
        #End Region

        ''' <summary>
        ''' تكوين النماذج
        ''' </summary>
        Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
            ' إزالة الاتفاقيات الافتراضية
            modelBuilder.Conventions.Remove(Of PluralizingTableNameConvention)()
            modelBuilder.Conventions.Remove(Of OneToManyCascadeDeleteConvention)()
            modelBuilder.Conventions.Remove(Of ManyToManyCascadeDeleteConvention)()

            ' تكوين دقة الأرقام العشرية
            modelBuilder.Properties(Of Decimal)().Configure(Function(config) config.HasPrecision(18, 3))

            ' تكوين العلاقات المخصصة
            ConfigureUserManagementRelationships(modelBuilder)
            ConfigureSettingsRelationships(modelBuilder)
            ConfigureAccountingRelationships(modelBuilder)
            ConfigurePayrollRelationships(modelBuilder)

            MyBase.OnModelCreating(modelBuilder)
        End Sub

        ''' <summary>
        ''' تكوين علاقات إدارة المستخدمين
        ''' </summary>
        Private Sub ConfigureUserManagementRelationships(modelBuilder As DbModelBuilder)
            ' علاقة المستخدم مع مجموعة المستخدمين
            modelBuilder.Entity(Of User)() _
                .HasOptional(Function(u) u.UserGroup) _
                .WithMany(Function(g) g.Users) _
                .HasForeignKey(Function(u) u.UserGroupId) _
                .WillCascadeOnDelete(False)

            ' علاقة صلاحيات المستخدم
            modelBuilder.Entity(Of UserPermission)() _
                .HasRequired(Function(up) up.User) _
                .WithMany(Function(u) u.UserPermissions) _
                .HasForeignKey(Function(up) up.UserId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of UserPermission)() _
                .HasRequired(Function(up) up.Permission) _
                .WithMany(Function(p) p.UserPermissions) _
                .HasForeignKey(Function(up) up.PermissionId) _
                .WillCascadeOnDelete(True)

            ' علاقة صلاحيات المجموعة
            modelBuilder.Entity(Of GroupPermission)() _
                .HasRequired(Function(gp) gp.UserGroup) _
                .WithMany(Function(g) g.GroupPermissions) _
                .HasForeignKey(Function(gp) gp.UserGroupId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of GroupPermission)() _
                .HasRequired(Function(gp) gp.Permission) _
                .WithMany(Function(p) p.GroupPermissions) _
                .HasForeignKey(Function(gp) gp.PermissionId) _
                .WillCascadeOnDelete(True)
        End Sub

        ''' <summary>
        ''' تكوين علاقات الإعدادات
        ''' </summary>
        Private Sub ConfigureSettingsRelationships(modelBuilder As DbModelBuilder)
            ' علاقة الدوائر مع الأقسام
            modelBuilder.Entity(Of Section)() _
                .HasRequired(Function(s) s.Department) _
                .WithMany(Function(d) d.Sections) _
                .HasForeignKey(Function(s) s.DepartmentId) _
                .WillCascadeOnDelete(False)

            ' علاقة الأقسام مع الشعب
            modelBuilder.Entity(Of Division)() _
                .HasRequired(Function(d) d.Section) _
                .WithMany(Function(s) s.Divisions) _
                .HasForeignKey(Function(d) d.SectionId) _
                .WillCascadeOnDelete(False)

            ' علاقة المصارف مع الفروع
            modelBuilder.Entity(Of BankBranch)() _
                .HasRequired(Function(b) b.Bank) _
                .WithMany(Function(bank) bank.BankBranches) _
                .HasForeignKey(Function(b) b.BankId) _
                .WillCascadeOnDelete(False)
        End Sub

        ''' <summary>
        ''' تكوين علاقات المحاسبة
        ''' </summary>
        Private Sub ConfigureAccountingRelationships(modelBuilder As DbModelBuilder)
            ' علاقة شجرة الحسابات (Self-Referencing)
            modelBuilder.Entity(Of ChartOfAccount)() _
                .HasOptional(Function(a) a.ParentAccount) _
                .WithMany(Function(a) a.ChildAccounts) _
                .HasForeignKey(Function(a) a.ParentAccountId) _
                .WillCascadeOnDelete(False)

            ' علاقة القيود اليومية مع أنواع القيود
            modelBuilder.Entity(Of JournalEntry)() _
                .HasRequired(Function(je) je.EntryType) _
                .WithMany(Function(jet) jet.JournalEntries) _
                .HasForeignKey(Function(je) je.EntryTypeId) _
                .WillCascadeOnDelete(False)

            ' علاقة تفاصيل القيود مع القيود والحسابات
            modelBuilder.Entity(Of JournalEntryDetail)() _
                .HasRequired(Function(jed) jed.JournalEntry) _
                .WithMany(Function(je) je.JournalEntryDetails) _
                .HasForeignKey(Function(jed) jed.EntryId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of JournalEntryDetail)() _
                .HasRequired(Function(jed) jed.Account) _
                .WithMany(Function(a) a.JournalEntryDetails) _
                .HasForeignKey(Function(jed) jed.AccountId) _
                .WillCascadeOnDelete(False)
        End Sub

        ''' <summary>
        ''' تكوين علاقات الرواتب
        ''' </summary>
        Private Sub ConfigurePayrollRelationships(modelBuilder As DbModelBuilder)
            ' علاقة المراحل مع الدرجات الوظيفية
            modelBuilder.Entity(Of JobStep)() _
                .HasRequired(Function(js) js.JobGrade) _
                .WithMany(Function(jg) jg.JobSteps) _
                .HasForeignKey(Function(js) js.GradeId) _
                .WillCascadeOnDelete(False)

            ' علاقات الموظفين
            modelBuilder.Entity(Of Employee)() _
                .HasRequired(Function(e) e.JobTitle) _
                .WithMany(Function(jt) jt.Employees) _
                .HasForeignKey(Function(e) e.JobTitleId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Employee)() _
                .HasRequired(Function(e) e.Position) _
                .WithMany(Function(p) p.Employees) _
                .HasForeignKey(Function(e) e.PositionId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Employee)() _
                .HasRequired(Function(e) e.Qualification) _
                .WithMany(Function(q) q.Employees) _
                .HasForeignKey(Function(e) e.QualificationId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Employee)() _
                .HasRequired(Function(e) e.JobGrade) _
                .WithMany(Function(jg) jg.Employees) _
                .HasForeignKey(Function(e) e.GradeId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of Employee)() _
                .HasRequired(Function(e) e.JobStep) _
                .WithMany(Function(js) js.Employees) _
                .HasForeignKey(Function(e) e.StepId) _
                .WillCascadeOnDelete(False)

            ' علاقات المخصصات والاستقطاعات
            modelBuilder.Entity(Of EmployeeAllowance)() _
                .HasRequired(Function(ea) ea.Employee) _
                .WithMany(Function(e) e.EmployeeAllowances) _
                .HasForeignKey(Function(ea) ea.EmployeeId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of EmployeeAllowance)() _
                .HasRequired(Function(ea) ea.AllowanceType) _
                .WithMany(Function(at) at.EmployeeAllowances) _
                .HasForeignKey(Function(ea) ea.AllowanceTypeId) _
                .WillCascadeOnDelete(False)

            modelBuilder.Entity(Of EmployeeDeduction)() _
                .HasRequired(Function(ed) ed.Employee) _
                .WithMany(Function(e) e.EmployeeDeductions) _
                .HasForeignKey(Function(ed) ed.EmployeeId) _
                .WillCascadeOnDelete(True)

            modelBuilder.Entity(Of EmployeeDeduction)() _
                .HasRequired(Function(ed) ed.DeductionType) _
                .WithMany(Function(dt) dt.EmployeeDeductions) _
                .HasForeignKey(Function(ed) ed.DeductionTypeId) _
                .WillCascadeOnDelete(False)

            ' علاقة سجلات الرواتب
            modelBuilder.Entity(Of PayrollRecord)() _
                .HasRequired(Function(pr) pr.Employee) _
                .WithMany(Function(e) e.PayrollRecords) _
                .HasForeignKey(Function(pr) pr.EmployeeId) _
                .WillCascadeOnDelete(True)
        End Sub

    End Class

End Namespace
