Imports DevExpress.XtraEditors
Imports DevExpress.XtraLayout
Imports System.Globalization
Imports System.Threading
Imports MinistryAccountingSystem.Models.UserManagement
Imports MinistryAccountingSystem.Services

''' <summary>
''' نموذج تسجيل الدخول
''' </summary>
Public Class LoginForm
    Inherits XtraForm

    Private WithEvents layoutControl As LayoutControl
    Private WithEvents txtUsername As TextEdit
    Private WithEvents txtPassword As TextEdit
    Private WithEvents btnLogin As SimpleButton
    Private WithEvents btnCancel As SimpleButton
    Private WithEvents lblTitle As LabelControl
    Private WithEvents lblOrganization As LabelControl
    Private WithEvents picLogo As PictureEdit

    Private userService As UserManagementService
    Private loginAttempts As Integer = 0

    ''' <summary>
    ''' المستخدم الذي تم تسجيل دخوله بنجاح
    ''' </summary>
    Public Property LoggedInUser As User

    ''' <summary>
    ''' منشئ نموذج تسجيل الدخول
    ''' </summary>
    Public Sub New()
        InitializeComponent()
        InitializeArabicSupport()
        userService = New UserManagementService()
    End Sub

    ''' <summary>
    ''' تهيئة المكونات
    ''' </summary>
    Private Sub InitializeComponent()
        Me.SuspendLayout()

        ' إعدادات النموذج
        Me.Text = "تسجيل الدخول - نظام محاسبة وزارة الشباب والرياضة"
        Me.Size = New Size(450, 350)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True

        ' إنشاء Layout Control
        layoutControl = New LayoutControl()
        layoutControl.Dock = DockStyle.Fill
        layoutControl.RightToLeft = RightToLeft.Yes

        ' شعار المؤسسة
        picLogo = New PictureEdit()
        picLogo.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Never
        picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom
        ' picLogo.Image = My.Resources.MinistryLogo ' يجب إضافة شعار الوزارة

        ' عنوان النظام
        lblTitle = New LabelControl()
        lblTitle.Text = "نظام محاسبة وزارة الشباب والرياضة"
        lblTitle.Appearance.Font = New Font("Tahoma", 14, FontStyle.Bold)
        lblTitle.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center

        ' اسم المؤسسة
        lblOrganization = New LabelControl()
        lblOrganization.Text = "وزارة الشباب والرياضة"
        lblOrganization.Appearance.Font = New Font("Tahoma", 12, FontStyle.Regular)
        lblOrganization.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center

        ' حقل اسم المستخدم
        txtUsername = New TextEdit()
        txtUsername.Properties.NullText = "اسم المستخدم"
        txtUsername.Properties.Appearance.Font = New Font("Tahoma", 10)
        txtUsername.Size = New Size(250, 25)

        ' حقل كلمة المرور
        txtPassword = New TextEdit()
        txtPassword.Properties.UseSystemPasswordChar = True
        txtPassword.Properties.NullText = "كلمة المرور"
        txtPassword.Properties.Appearance.Font = New Font("Tahoma", 10)
        txtPassword.Size = New Size(250, 25)

        ' زر تسجيل الدخول
        btnLogin = New SimpleButton()
        btnLogin.Text = "تسجيل الدخول"
        btnLogin.Appearance.Font = New Font("Tahoma", 10, FontStyle.Bold)
        btnLogin.Size = New Size(120, 35)

        ' زر الإلغاء
        btnCancel = New SimpleButton()
        btnCancel.Text = "إلغاء"
        btnCancel.Appearance.Font = New Font("Tahoma", 10)
        btnCancel.Size = New Size(120, 35)

        ' إضافة المكونات إلى Layout
        layoutControl.Controls.Add(picLogo)
        layoutControl.Controls.Add(lblTitle)
        layoutControl.Controls.Add(lblOrganization)
        layoutControl.Controls.Add(txtUsername)
        layoutControl.Controls.Add(txtPassword)
        layoutControl.Controls.Add(btnLogin)
        layoutControl.Controls.Add(btnCancel)

        ' تكوين Layout
        SetupLayout()

        ' إضافة Layout إلى النموذج
        Me.Controls.Add(layoutControl)

        ' تعيين الأزرار الافتراضية
        Me.AcceptButton = btnLogin
        Me.CancelButton = btnCancel

        Me.ResumeLayout(False)
    End Sub

    ''' <summary>
    ''' تهيئة دعم اللغة العربية
    ''' </summary>
    Private Sub InitializeArabicSupport()
        Dim arabicCulture As New CultureInfo("ar-SA")
        Thread.CurrentThread.CurrentCulture = arabicCulture
        Thread.CurrentThread.CurrentUICulture = arabicCulture

        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
    End Sub

    ''' <summary>
    ''' تكوين تخطيط العناصر
    ''' </summary>
    Private Sub SetupLayout()
        Dim layoutControlGroup As LayoutControlGroup = layoutControl.Root
        layoutControlGroup.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True
        layoutControlGroup.GroupBordersVisible = False
        layoutControlGroup.Padding = New DevExpress.XtraLayout.Utils.Padding(20)

        ' إنشاء عناصر التخطيط
        Dim logoItem As LayoutControlItem = layoutControlGroup.AddItem("", picLogo)
        logoItem.TextVisible = False
        logoItem.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        logoItem.MaxSize = New Size(100, 80)
        logoItem.MinSize = New Size(100, 80)

        Dim titleItem As LayoutControlItem = layoutControlGroup.AddItem("", lblTitle)
        titleItem.TextVisible = False

        Dim orgItem As LayoutControlItem = layoutControlGroup.AddItem("", lblOrganization)
        orgItem.TextVisible = False

        ' مساحة فارغة
        Dim emptySpaceItem1 As EmptySpaceItem = layoutControlGroup.AddItem()
        emptySpaceItem1.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        emptySpaceItem1.MaxSize = New Size(0, 20)
        emptySpaceItem1.MinSize = New Size(0, 20)

        Dim usernameItem As LayoutControlItem = layoutControlGroup.AddItem("اسم المستخدم:", txtUsername)
        usernameItem.TextSize = New Size(80, 13)

        Dim passwordItem As LayoutControlItem = layoutControlGroup.AddItem("كلمة المرور:", txtPassword)
        passwordItem.TextSize = New Size(80, 13)

        ' مساحة فارغة
        Dim emptySpaceItem2 As EmptySpaceItem = layoutControlGroup.AddItem()
        emptySpaceItem2.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom
        emptySpaceItem2.MaxSize = New Size(0, 15)
        emptySpaceItem2.MinSize = New Size(0, 15)

        ' مجموعة الأزرار
        Dim buttonGroup As LayoutControlGroup = layoutControlGroup.AddGroup()
        buttonGroup.GroupBordersVisible = False
        buttonGroup.LayoutMode = DevExpress.XtraLayout.Utils.LayoutMode.Flow
        buttonGroup.FlowDirection = DevExpress.XtraLayout.Utils.FlowDirection.LeftToRight

        Dim loginItem As LayoutControlItem = buttonGroup.AddItem("", btnLogin)
        loginItem.TextVisible = False

        Dim cancelItem As LayoutControlItem = buttonGroup.AddItem("", btnCancel)
        cancelItem.TextVisible = False

        ' تعيين محاذاة العناصر
        logoItem.ContentAlignment = ContentAlignment.MiddleCenter
        titleItem.ContentAlignment = ContentAlignment.MiddleCenter
        orgItem.ContentAlignment = ContentAlignment.MiddleCenter
        buttonGroup.ContentAlignment = ContentAlignment.MiddleCenter
    End Sub

    ''' <summary>
    ''' معالج النقر على زر تسجيل الدخول
    ''' </summary>
    Private Sub btnLogin_Click(sender As Object, e As EventArgs) Handles btnLogin.Click
        Try
            ' التحقق من صحة البيانات المدخلة
            If Not ValidateInput() Then
                Return
            End If

            ' تعطيل الأزرار أثناء المعالجة
            btnLogin.Enabled = False
            btnCancel.Enabled = False
            Me.Cursor = Cursors.WaitCursor

            ' محاولة تسجيل الدخول
            Dim result = userService.Login(txtUsername.Text.Trim(), txtPassword.Text)

            If result.IsSuccess Then
                LoggedInUser = result.Data
                Me.DialogResult = DialogResult.OK
                Me.Close()
            Else
                loginAttempts += 1
                XtraMessageBox.Show(result.Message, "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Warning)

                ' مسح كلمة المرور
                txtPassword.Text = ""
                txtPassword.Focus()

                ' حظر النموذج بعد 3 محاولات فاشلة
                If loginAttempts >= 3 Then
                    XtraMessageBox.Show("تم تجاوز عدد محاولات تسجيل الدخول المسموحة. سيتم إغلاق التطبيق.", "تحذير أمني", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                    Application.Exit()
                End If
            End If

        Catch ex As Exception
            XtraMessageBox.Show($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Finally
            ' إعادة تفعيل الأزرار
            btnLogin.Enabled = True
            btnCancel.Enabled = True
            Me.Cursor = Cursors.Default
        End Try
    End Sub

    ''' <summary>
    ''' معالج النقر على زر الإلغاء
    ''' </summary>
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    ''' <summary>
    ''' التحقق من صحة البيانات المدخلة
    ''' </summary>
    ''' <returns>true إذا كانت البيانات صحيحة</returns>
    Private Function ValidateInput() As Boolean
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            XtraMessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return False
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            XtraMessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' معالج الضغط على مفتاح في حقل كلمة المرور
    ''' </summary>
    Private Sub txtPassword_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtPassword.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            btnLogin.PerformClick()
            e.Handled = True
        End If
    End Sub

    ''' <summary>
    ''' معالج الضغط على مفتاح في حقل اسم المستخدم
    ''' </summary>
    Private Sub txtUsername_KeyPress(sender As Object, e As KeyPressEventArgs) Handles txtUsername.KeyPress
        If e.KeyChar = Convert.ToChar(Keys.Enter) Then
            txtPassword.Focus()
            e.Handled = True
        End If
    End Sub

    ''' <summary>
    ''' معالج تحميل النموذج
    ''' </summary>
    Private Sub LoginForm_Load(sender As Object, e As EventArgs) Handles Me.Load
        txtUsername.Focus()
    End Sub

    ''' <summary>
    ''' معالج إغلاق النموذج
    ''' </summary>
    Private Sub LoginForm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        Try
            userService?.Dispose()
        Catch
            ' تجاهل أخطاء التنظيف
        End Try
    End Sub

End Class
