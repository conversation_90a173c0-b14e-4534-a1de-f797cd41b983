Imports System.Windows.Forms
Imports System.Drawing
Imports System.Globalization
Imports System.Threading

''' <summary>
''' نسخة مبسطة من نظام محاسبة وزارة الشباب والرياضة للعرض التوضيحي
''' </summary>
Public Class SimpleMinistryApp

    ''' <summary>
    ''' نقطة دخول التطبيق
    ''' </summary>
    <STAThread>
    Public Shared Sub Main()
        ' تعيين الثقافة العربية
        Dim arabicCulture As New CultureInfo("ar-SA")
        Thread.CurrentThread.CurrentCulture = arabicCulture
        Thread.CurrentThread.CurrentUICulture = arabicCulture

        Application.EnableVisualStyles()
        Application.SetCompatibleTextRenderingDefault(False)

        ' عرض نموذج تسجيل الدخول
        Dim loginForm As New SimpleLoginForm()
        If loginForm.ShowDialog() = DialogResult.OK Then
            ' إذا تم تسجيل الدخول بنجاح، عرض النموذج الرئيسي
            Application.Run(New SimpleMainForm(loginForm.LoggedInUser))
        End If
    End Sub

End Class

''' <summary>
''' نموذج تسجيل الدخول المبسط
''' </summary>
Public Class SimpleLoginForm
    Inherits Form

    Private txtUsername As TextBox
    Private txtPassword As TextBox
    Private btnLogin As Button
    Private btnCancel As Button
    Private lblTitle As Label
    Private lblUsername As Label
    Private lblPassword As Label

    Public Property LoggedInUser As String

    Public Sub New()
        InitializeComponent()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = "تسجيل الدخول - نظام محاسبة وزارة الشباب والرياضة"
        Me.Size = New Size(400, 300)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True

        ' العنوان
        lblTitle = New Label()
        lblTitle.Text = "نظام محاسبة وزارة الشباب والرياضة"
        lblTitle.Font = New Font("Tahoma", 14, FontStyle.Bold)
        lblTitle.ForeColor = Color.DarkBlue
        lblTitle.TextAlign = ContentAlignment.MiddleCenter
        lblTitle.Location = New Point(50, 30)
        lblTitle.Size = New Size(300, 30)

        ' تسمية اسم المستخدم
        lblUsername = New Label()
        lblUsername.Text = "اسم المستخدم:"
        lblUsername.Font = New Font("Tahoma", 10)
        lblUsername.Location = New Point(280, 80)
        lblUsername.Size = New Size(100, 20)

        ' حقل اسم المستخدم
        txtUsername = New TextBox()
        txtUsername.Font = New Font("Tahoma", 10)
        txtUsername.Location = New Point(50, 80)
        txtUsername.Size = New Size(220, 25)
        txtUsername.Text = "admin"

        ' تسمية كلمة المرور
        lblPassword = New Label()
        lblPassword.Text = "كلمة المرور:"
        lblPassword.Font = New Font("Tahoma", 10)
        lblPassword.Location = New Point(280, 120)
        lblPassword.Size = New Size(100, 20)

        ' حقل كلمة المرور
        txtPassword = New TextBox()
        txtPassword.Font = New Font("Tahoma", 10)
        txtPassword.UseSystemPasswordChar = True
        txtPassword.Location = New Point(50, 120)
        txtPassword.Size = New Size(220, 25)
        txtPassword.Text = "admin123"

        ' زر تسجيل الدخول
        btnLogin = New Button()
        btnLogin.Text = "تسجيل الدخول"
        btnLogin.Font = New Font("Tahoma", 10, FontStyle.Bold)
        btnLogin.BackColor = Color.LightBlue
        btnLogin.Location = New Point(180, 170)
        btnLogin.Size = New Size(100, 35)
        AddHandler btnLogin.Click, AddressOf btnLogin_Click

        ' زر الإلغاء
        btnCancel = New Button()
        btnCancel.Text = "إلغاء"
        btnCancel.Font = New Font("Tahoma", 10)
        btnCancel.BackColor = Color.LightGray
        btnCancel.Location = New Point(70, 170)
        btnCancel.Size = New Size(100, 35)
        AddHandler btnCancel.Click, AddressOf btnCancel_Click

        ' إضافة العناصر للنموذج
        Me.Controls.AddRange({lblTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel})

        Me.AcceptButton = btnLogin
        Me.CancelButton = btnCancel
    End Sub

    Private Sub btnLogin_Click(sender As Object, e As EventArgs)
        If String.IsNullOrWhiteSpace(txtUsername.Text) Then
            MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtUsername.Focus()
            Return
        End If

        If String.IsNullOrWhiteSpace(txtPassword.Text) Then
            MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtPassword.Focus()
            Return
        End If

        ' التحقق من بيانات تسجيل الدخول
        If txtUsername.Text = "admin" AndAlso txtPassword.Text = "admin123" Then
            LoggedInUser = txtUsername.Text
            MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.DialogResult = DialogResult.OK
            Me.Close()
        Else
            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            txtPassword.Text = ""
            txtPassword.Focus()
        End If
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub
End Class

''' <summary>
''' النموذج الرئيسي المبسط
''' </summary>
Public Class SimpleMainForm
    Inherits Form

    Private currentUser As String
    Private menuStrip As MenuStrip
    Private statusStrip As StatusStrip
    Private lblWelcome As Label

    Public Sub New(user As String)
        currentUser = user
        InitializeComponent()
        SetupMenu()
        SetupStatusBar()
    End Sub

    Private Sub InitializeComponent()
        Me.Text = $"نظام محاسبة وزارة الشباب والرياضة - {currentUser}"
        Me.WindowState = FormWindowState.Maximized
        Me.IsMdiContainer = True
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.LightSteelBlue

        ' رسالة الترحيب
        lblWelcome = New Label()
        lblWelcome.Text = $"مرحباً بك في نظام محاسبة وزارة الشباب والرياضة{Environment.NewLine}{Environment.NewLine}المستخدم: {currentUser}{Environment.NewLine}التاريخ: {DateTime.Now:yyyy/MM/dd}{Environment.NewLine}الوقت: {DateTime.Now:HH:mm:ss}"
        lblWelcome.Font = New Font("Tahoma", 16, FontStyle.Bold)
        lblWelcome.ForeColor = Color.DarkBlue
        lblWelcome.TextAlign = ContentAlignment.MiddleCenter
        lblWelcome.Dock = DockStyle.Fill
        Me.Controls.Add(lblWelcome)
    End Sub

    Private Sub SetupMenu()
        menuStrip = New MenuStrip()
        menuStrip.RightToLeft = RightToLeft.Yes

        ' قائمة إدارة المستخدمين
        Dim usersMenu As New ToolStripMenuItem("إدارة المستخدمين")
        usersMenu.DropDownItems.Add("المستخدمون", Nothing, AddressOf ShowUsers)
        usersMenu.DropDownItems.Add("مجموعات المستخدمين", Nothing, AddressOf ShowUserGroups)
        usersMenu.DropDownItems.Add("الصلاحيات", Nothing, AddressOf ShowPermissions)

        ' قائمة الإعدادات
        Dim settingsMenu As New ToolStripMenuItem("الإعدادات")
        settingsMenu.DropDownItems.Add("معلومات المؤسسة", Nothing, AddressOf ShowOrganizationInfo)
        settingsMenu.DropDownItems.Add("الدوائر", Nothing, AddressOf ShowDepartments)
        settingsMenu.DropDownItems.Add("الأقسام", Nothing, AddressOf ShowSections)
        settingsMenu.DropDownItems.Add("المصارف", Nothing, AddressOf ShowBanks)

        ' قائمة المحاسبة
        Dim accountingMenu As New ToolStripMenuItem("المحاسبة")
        accountingMenu.DropDownItems.Add("دليل الحسابات", Nothing, AddressOf ShowChartOfAccounts)
        accountingMenu.DropDownItems.Add("القيود اليومية", Nothing, AddressOf ShowJournalEntries)
        accountingMenu.DropDownItems.Add("سندات القبض", Nothing, AddressOf ShowReceiptVouchers)
        accountingMenu.DropDownItems.Add("سندات الصرف", Nothing, AddressOf ShowPaymentVouchers)

        ' قائمة الرواتب
        Dim payrollMenu As New ToolStripMenuItem("الرواتب")
        payrollMenu.DropDownItems.Add("الموظفون", Nothing, AddressOf ShowEmployees)
        payrollMenu.DropDownItems.Add("المناصب", Nothing, AddressOf ShowPositions)
        payrollMenu.DropDownItems.Add("معالجة الرواتب", Nothing, AddressOf ShowPayrollProcessing)

        ' قائمة التقارير
        Dim reportsMenu As New ToolStripMenuItem("التقارير")
        reportsMenu.DropDownItems.Add("التقارير المالية", Nothing, AddressOf ShowFinancialReports)
        reportsMenu.DropDownItems.Add("تقارير الرواتب", Nothing, AddressOf ShowPayrollReports)

        ' قائمة المساعدة
        Dim helpMenu As New ToolStripMenuItem("مساعدة")
        helpMenu.DropDownItems.Add("حول البرنامج", Nothing, AddressOf ShowAbout)
        helpMenu.DropDownItems.Add("دليل المستخدم", Nothing, AddressOf ShowUserGuide)

        menuStrip.Items.AddRange({usersMenu, settingsMenu, accountingMenu, payrollMenu, reportsMenu, helpMenu})
        Me.MainMenuStrip = menuStrip
        Me.Controls.Add(menuStrip)
    End Sub

    Private Sub SetupStatusBar()
        statusStrip = New StatusStrip()
        statusStrip.RightToLeft = RightToLeft.Yes

        Dim statusLabel As New ToolStripStatusLabel("جاهز")
        Dim userLabel As New ToolStripStatusLabel($"المستخدم: {currentUser}")
        Dim dateLabel As New ToolStripStatusLabel($"التاريخ: {DateTime.Now:yyyy/MM/dd}")
        Dim timeLabel As New ToolStripStatusLabel($"الوقت: {DateTime.Now:HH:mm:ss}")

        statusStrip.Items.AddRange({statusLabel, userLabel, dateLabel, timeLabel})
        Me.Controls.Add(statusStrip)
    End Sub

    ' معالجات الأحداث للقوائم
    Private Sub ShowUsers(sender As Object, e As EventArgs)
        ShowModuleForm("إدارة المستخدمين", "هنا يتم إدارة المستخدمين وصلاحياتهم")
    End Sub

    Private Sub ShowUserGroups(sender As Object, e As EventArgs)
        ShowModuleForm("مجموعات المستخدمين", "هنا يتم إدارة مجموعات المستخدمين")
    End Sub

    Private Sub ShowPermissions(sender As Object, e As EventArgs)
        ShowModuleForm("الصلاحيات", "هنا يتم إدارة صلاحيات النظام")
    End Sub

    Private Sub ShowOrganizationInfo(sender As Object, e As EventArgs)
        ShowModuleForm("معلومات المؤسسة", "هنا يتم إدارة معلومات وزارة الشباب والرياضة")
    End Sub

    Private Sub ShowDepartments(sender As Object, e As EventArgs)
        ShowModuleForm("الدوائر", "هنا يتم إدارة دوائر الوزارة")
    End Sub

    Private Sub ShowSections(sender As Object, e As EventArgs)
        ShowModuleForm("الأقسام", "هنا يتم إدارة أقسام الدوائر")
    End Sub

    Private Sub ShowBanks(sender As Object, e As EventArgs)
        ShowModuleForm("المصارف", "هنا يتم إدارة المصارف وفروعها")
    End Sub

    Private Sub ShowChartOfAccounts(sender As Object, e As EventArgs)
        ShowModuleForm("دليل الحسابات", "هنا يتم إدارة شجرة الحسابات المحاسبية")
    End Sub

    Private Sub ShowJournalEntries(sender As Object, e As EventArgs)
        ShowModuleForm("القيود اليومية", "هنا يتم إدخال وإدارة القيود المحاسبية")
    End Sub

    Private Sub ShowReceiptVouchers(sender As Object, e As EventArgs)
        ShowModuleForm("سندات القبض", "هنا يتم إدارة سندات القبض")
    End Sub

    Private Sub ShowPaymentVouchers(sender As Object, e As EventArgs)
        ShowModuleForm("سندات الصرف", "هنا يتم إدارة سندات الصرف")
    End Sub

    Private Sub ShowEmployees(sender As Object, e As EventArgs)
        ShowModuleForm("الموظفون", "هنا يتم إدارة بيانات الموظفين")
    End Sub

    Private Sub ShowPositions(sender As Object, e As EventArgs)
        ShowModuleForm("المناصب", "هنا يتم إدارة المناصب الوظيفية")
    End Sub

    Private Sub ShowPayrollProcessing(sender As Object, e As EventArgs)
        ShowModuleForm("معالجة الرواتب", "هنا يتم حساب ومعالجة رواتب الموظفين")
    End Sub

    Private Sub ShowFinancialReports(sender As Object, e As EventArgs)
        ShowModuleForm("التقارير المالية", "هنا يتم عرض التقارير المالية والمحاسبية")
    End Sub

    Private Sub ShowPayrollReports(sender As Object, e As EventArgs)
        ShowModuleForm("تقارير الرواتب", "هنا يتم عرض تقارير الرواتب والمخصصات")
    End Sub

    Private Sub ShowAbout(sender As Object, e As EventArgs)
        MessageBox.Show("نظام محاسبة وزارة الشباب والرياضة" & Environment.NewLine & 
                       "الإصدار 1.0.0" & Environment.NewLine & 
                       "تم التطوير باستخدام VB.NET" & Environment.NewLine & 
                       "جميع الحقوق محفوظة © 2024", 
                       "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ShowUserGuide(sender As Object, e As EventArgs)
        MessageBox.Show("دليل المستخدم سيكون متاحاً قريباً", "دليل المستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub ShowModuleForm(title As String, description As String)
        Dim moduleForm As New Form()
        moduleForm.Text = title
        moduleForm.Size = New Size(800, 600)
        moduleForm.StartPosition = FormStartPosition.CenterParent
        moduleForm.RightToLeft = RightToLeft.Yes
        moduleForm.RightToLeftLayout = True

        Dim lblDescription As New Label()
        lblDescription.Text = description
        lblDescription.Font = New Font("Tahoma", 12)
        lblDescription.TextAlign = ContentAlignment.MiddleCenter
        lblDescription.Dock = DockStyle.Fill
        lblDescription.ForeColor = Color.DarkBlue

        moduleForm.Controls.Add(lblDescription)
        moduleForm.ShowDialog(Me)
    End Sub
End Class
