Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

Namespace Models.Payroll

    ''' <summary>
    ''' نموذج المناصب
    ''' </summary>
    <Table("Positions")>
    Public Class Position
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property PositionId As Integer

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز المنصب")>
        Public Property PositionCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم المنصب")>
        Public Property PositionName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="مخصص المنصب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property PositionAllowance As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property Employees As ICollection(Of Employee) = New HashSet(Of Employee)
    End Class

    ''' <summary>
    ''' نموذج العناوين الوظيفية
    ''' </summary>
    <Table("JobTitles")>
    Public Class JobTitle
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property JobTitleId As Integer

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز العنوان الوظيفي")>
        Public Property JobTitleCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم العنوان الوظيفي")>
        Public Property JobTitleName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property Employees As ICollection(Of Employee) = New HashSet(Of Employee)
    End Class

    ''' <summary>
    ''' نموذج الشهادات العلمية
    ''' </summary>
    <Table("Qualifications")>
    Public Class Qualification
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property QualificationId As Integer

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز الشهادة")>
        Public Property QualificationCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الشهادة")>
        Public Property QualificationName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="مخصص الشهادة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property QualificationAllowance As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property Employees As ICollection(Of Employee) = New HashSet(Of Employee)
    End Class

    ''' <summary>
    ''' نموذج الدرجات الوظيفية
    ''' </summary>
    <Table("JobGrades")>
    Public Class JobGrade
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property GradeId As Integer

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز الدرجة")>
        Public Property GradeCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم الدرجة")>
        Public Property GradeName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="الراتب الأساسي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property BasicSalary As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property JobSteps As ICollection(Of JobStep) = New HashSet(Of JobStep)
        Public Overridable Property Employees As ICollection(Of Employee) = New HashSet(Of Employee)
    End Class

    ''' <summary>
    ''' نموذج المراحل
    ''' </summary>
    <Table("JobSteps")>
    Public Class JobStep
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property StepId As Integer

        <Display(Name:="الدرجة الوظيفية")>
        Public Property GradeId As Integer

        <Required>
        <StringLength(10)>
        <Display(Name:="رمز المرحلة")>
        Public Property StepCode As String

        <Required>
        <StringLength(200)>
        <Display(Name:="اسم المرحلة")>
        Public Property StepName As String

        <Display(Name:="رقم المرحلة")>
        Public Property StepNumber As Integer

        <Display(Name:="مبلغ المرحلة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property StepAmount As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        <ForeignKey("GradeId")>
        Public Overridable Property JobGrade As JobGrade

        Public Overridable Property Employees As ICollection(Of Employee) = New HashSet(Of Employee)
    End Class

    ''' <summary>
    ''' نموذج الموظفين
    ''' </summary>
    <Table("Employees")>
    Public Class Employee
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property EmployeeId As Integer

        <Required>
        <StringLength(20)>
        <Display(Name:="الرقم الوظيفي")>
        Public Property EmployeeNumber As String

        <Required>
        <StringLength(100)>
        <Display(Name:="الاسم الأول")>
        Public Property FirstName As String

        <Required>
        <StringLength(100)>
        <Display(Name:="الاسم الثاني")>
        Public Property MiddleName As String

        <Required>
        <StringLength(100)>
        <Display(Name:="الاسم الثالث")>
        Public Property LastName As String

        <Required>
        <StringLength(100)>
        <Display(Name:="اسم الأم")>
        Public Property MotherName As String

        <StringLength(50)>
        <Display(Name:="رقم الـ IBAN")>
        Public Property IBANNumber As String

        <Display(Name:="العنوان الوظيفي")>
        Public Property JobTitleId As Integer

        <Display(Name:="المنصب")>
        Public Property PositionId As Integer

        <Display(Name:="الشهادة العلمية")>
        Public Property QualificationId As Integer

        <Display(Name:="الدرجة الوظيفية")>
        Public Property GradeId As Integer

        <Display(Name:="المرحلة")>
        Public Property StepId As Integer

        <Display(Name:="الدائرة")>
        Public Property DepartmentId As Integer

        <Display(Name:="القسم")>
        Public Property SectionId As Integer?

        <Display(Name:="الشعبة")>
        Public Property DivisionId As Integer?

        <Display(Name:="تاريخ التعيين")>
        Public Property HireDate As DateTime

        <Display(Name:="الراتب الأساسي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property BasicSalary As Decimal = 0

        <Display(Name:="العلاوة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property Allowance As Decimal = 0

        <Display(Name:="فرق راتب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property SalaryDifference As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        <ForeignKey("JobTitleId")>
        Public Overridable Property JobTitle As JobTitle

        <ForeignKey("PositionId")>
        Public Overridable Property Position As Position

        <ForeignKey("QualificationId")>
        Public Overridable Property Qualification As Qualification

        <ForeignKey("GradeId")>
        Public Overridable Property JobGrade As JobGrade

        <ForeignKey("StepId")>
        Public Overridable Property JobStep As JobStep

        Public Overridable Property PayrollRecords As ICollection(Of PayrollRecord) = New HashSet(Of PayrollRecord)
        Public Overridable Property EmployeeAllowances As ICollection(Of EmployeeAllowance) = New HashSet(Of EmployeeAllowance)
        Public Overridable Property EmployeeDeductions As ICollection(Of EmployeeDeduction) = New HashSet(Of EmployeeDeduction)
    End Class

    ''' <summary>
    ''' نموذج أنواع المخصصات
    ''' </summary>
    <Table("AllowanceTypes")>
    Public Class AllowanceType
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property AllowanceTypeId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="اسم المخصص")>
        Public Property AllowanceName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="مبلغ ثابت")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property FixedAmount As Decimal = 0

        <Display(Name:="نسبة مئوية")>
        <Column(TypeName:="decimal(5,2)")>
        Public Property Percentage As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property EmployeeAllowances As ICollection(Of EmployeeAllowance) = New HashSet(Of EmployeeAllowance)
    End Class

    ''' <summary>
    ''' نموذج مخصصات الموظفين
    ''' </summary>
    <Table("EmployeeAllowances")>
    Public Class EmployeeAllowance
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property EmployeeAllowanceId As Integer

        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Display(Name:="نوع المخصص")>
        Public Property AllowanceTypeId As Integer

        <Display(Name:="المبلغ")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property Amount As Decimal = 0

        <Display(Name:="تاريخ البداية")>
        Public Property StartDate As DateTime = DateTime.Now

        <Display(Name:="تاريخ النهاية")>
        Public Property EndDate As DateTime?

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee

        <ForeignKey("AllowanceTypeId")>
        Public Overridable Property AllowanceType As AllowanceType
    End Class

    ''' <summary>
    ''' نموذج أنواع الاستقطاعات
    ''' </summary>
    <Table("DeductionTypes")>
    Public Class DeductionType
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property DeductionTypeId As Integer

        <Required>
        <StringLength(50)>
        <Display(Name:="اسم الاستقطاع")>
        Public Property DeductionName As String

        <StringLength(500)>
        <Display(Name:="الوصف")>
        Public Property Description As String

        <Display(Name:="مبلغ ثابت")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property FixedAmount As Decimal = 0

        <Display(Name:="نسبة مئوية")>
        <Column(TypeName:="decimal(5,2)")>
        Public Property Percentage As Decimal = 0

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        Public Overridable Property EmployeeDeductions As ICollection(Of EmployeeDeduction) = New HashSet(Of EmployeeDeduction)
    End Class

    ''' <summary>
    ''' نموذج استقطاعات الموظفين
    ''' </summary>
    <Table("EmployeeDeductions")>
    Public Class EmployeeDeduction
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property EmployeeDeductionId As Integer

        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Display(Name:="نوع الاستقطاع")>
        Public Property DeductionTypeId As Integer

        <Display(Name:="المبلغ")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property Amount As Decimal = 0

        <Display(Name:="تاريخ البداية")>
        Public Property StartDate As DateTime = DateTime.Now

        <Display(Name:="تاريخ النهاية")>
        Public Property EndDate As DateTime?

        <Display(Name:="نشط")>
        Public Property IsActive As Boolean = True

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee

        <ForeignKey("DeductionTypeId")>
        Public Overridable Property DeductionType As DeductionType
    End Class

    ''' <summary>
    ''' نموذج سجلات الرواتب الشهرية
    ''' </summary>
    <Table("PayrollRecords")>
    Public Class PayrollRecord
        <Key>
        <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
        Public Property PayrollRecordId As Integer

        <Display(Name:="الموظف")>
        Public Property EmployeeId As Integer

        <Display(Name:="السنة")>
        Public Property PayrollYear As Integer

        <Display(Name:="الشهر")>
        Public Property PayrollMonth As Integer

        <Display(Name:="الراتب الأساسي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property BasicSalary As Decimal = 0

        <Display(Name:="العلاوة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property Allowance As Decimal = 0

        <Display(Name:="فرق راتب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property SalaryDifference As Decimal = 0

        ' المخصصات
        <Display(Name:="مخصص المنصب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property PositionAllowance As Decimal = 0

        <Display(Name:="مخصص الإعالة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property FamilyAllowance As Decimal = 0

        <Display(Name:="مخصص الأولاد")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property ChildrenAllowance As Decimal = 0

        <Display(Name:="المخصص الهندسي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property EngineeringAllowance As Decimal = 0

        <Display(Name:="مخصص الشهادة العلمية")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property QualificationAllowance As Decimal = 0

        <Display(Name:="مخصص الحرفة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property CraftAllowance As Decimal = 0

        <Display(Name:="مخصص الخطورة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property DangerAllowance As Decimal = 0

        <Display(Name:="مخصص الموقع الجغرافي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property LocationAllowance As Decimal = 0

        <Display(Name:="المخصص الجامعي")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property UniversityAllowance As Decimal = 0

        ' الاستقطاعات
        <Display(Name:="ضريبة الدخل")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property IncomeTax As Decimal = 0

        <Display(Name:="تقاعد الموظفين")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property EmployeeRetirement As Decimal = 0

        <Display(Name:="مساهمة الدائرة")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property DepartmentContribution As Decimal = 0

        <Display(Name:="حماية اجتماعية")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property SocialProtection As Decimal = 0

        <Display(Name:="دوائر تنفيذ")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property ExecutionDepartments As Decimal = 0

        <Display(Name:="قروض وسلف مصرفية")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property BankLoans As Decimal = 0

        <Display(Name:="إجمالي المخصصات")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property TotalAllowances As Decimal = 0

        <Display(Name:="إجمالي الاستقطاعات")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property TotalDeductions As Decimal = 0

        <Display(Name:="إجمالي الراتب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property GrossSalary As Decimal = 0

        <Display(Name:="صافي الراتب")>
        <Column(TypeName:="decimal(18,3)")>
        Public Property NetSalary As Decimal = 0

        <Display(Name:="معتمد")>
        Public Property IsApproved As Boolean = False

        <Display(Name:="تاريخ الاعتماد")>
        Public Property ApprovedDate As DateTime?

        <Display(Name:="المعتمد بواسطة")>
        Public Property ApprovedBy As Integer?

        <Display(Name:="منشئ السجل")>
        Public Property CreatedBy As Integer

        <Display(Name:="تاريخ الإنشاء")>
        Public Property CreatedDate As DateTime = DateTime.Now

        ' العلاقات
        <ForeignKey("EmployeeId")>
        Public Overridable Property Employee As Employee
    End Class

End Namespace
