Imports System.IO
Imports System.Data
Imports ClosedXML.Excel
Imports iTextSharp.text
Imports iTextSharp.text.pdf
Imports System.Globalization

Namespace Helpers

    ''' <summary>
    ''' مساعد التقارير والتصدير
    ''' </summary>
    Public Class ReportHelper

        ''' <summary>
        ''' تصدير البيانات إلى ملف Excel
        ''' </summary>
        ''' <param name="dataTable">جدول البيانات</param>
        ''' <param name="fileName">اسم الملف</param>
        ''' <param name="sheetName">اسم الورقة</param>
        ''' <returns>مسار الملف المحفوظ</returns>
        Public Shared Function ExportToExcel(dataTable As DataTable, fileName As String, Optional sheetName As String = "البيانات") As String
            Try
                Dim reportsPath As String = My.Settings.ReportsPath
                If Not Directory.Exists(reportsPath) Then
                    Directory.CreateDirectory(reportsPath)
                End If

                Dim fullPath As String = Path.Combine(reportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx")

                Using workbook As New XLWorkbook()
                    Dim worksheet = workbook.Worksheets.Add(sheetName)
                    
                    ' تعيين اتجاه النص من اليمين إلى اليسار
                    worksheet.RightToLeft = True

                    ' إضافة رؤوس الأعمدة
                    For i As Integer = 0 To dataTable.Columns.Count - 1
                        worksheet.Cell(1, i + 1).Value = dataTable.Columns(i).ColumnName
                        worksheet.Cell(1, i + 1).Style.Font.Bold = True
                        worksheet.Cell(1, i + 1).Style.Fill.BackgroundColor = XLColor.LightGray
                        worksheet.Cell(1, i + 1).Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center
                    Next

                    ' إضافة البيانات
                    For row As Integer = 0 To dataTable.Rows.Count - 1
                        For col As Integer = 0 To dataTable.Columns.Count - 1
                            Dim cellValue = dataTable.Rows(row)(col)
                            worksheet.Cell(row + 2, col + 1).Value = If(cellValue Is DBNull.Value, "", cellValue.ToString())
                        Next
                    Next

                    ' تنسيق الجدول
                    Dim range = worksheet.Range(1, 1, dataTable.Rows.Count + 1, dataTable.Columns.Count)
                    range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin
                    range.Style.Border.InsideBorder = XLBorderStyleValues.Thin

                    ' ضبط عرض الأعمدة تلقائياً
                    worksheet.ColumnsUsed().AdjustToContents()

                    workbook.SaveAs(fullPath)
                End Using

                Return fullPath

            Catch ex As Exception
                Throw New Exception($"خطأ في تصدير البيانات إلى Excel: {ex.Message}")
            End Try
        End Function

        ''' <summary>
        ''' تصدير البيانات إلى ملف PDF
        ''' </summary>
        ''' <param name="dataTable">جدول البيانات</param>
        ''' <param name="fileName">اسم الملف</param>
        ''' <param name="title">عنوان التقرير</param>
        ''' <returns>مسار الملف المحفوظ</returns>
        Public Shared Function ExportToPDF(dataTable As DataTable, fileName As String, Optional title As String = "تقرير") As String
            Try
                Dim reportsPath As String = My.Settings.ReportsPath
                If Not Directory.Exists(reportsPath) Then
                    Directory.CreateDirectory(reportsPath)
                End If

                Dim fullPath As String = Path.Combine(reportsPath, $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf")

                Using fs As New FileStream(fullPath, FileMode.Create)
                    Dim document As New Document(PageSize.A4.Rotate(), 25, 25, 30, 30)
                    Dim writer As PdfWriter = PdfWriter.GetInstance(document, fs)

                    document.Open()

                    ' إضافة خط عربي (يجب توفير ملف الخط)
                    Dim arabicFont As BaseFont
                    Try
                        arabicFont = BaseFont.CreateFont("c:\windows\fonts\arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED)
                    Catch
                        arabicFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED)
                    End Try

                    Dim titleFont As New Font(arabicFont, 16, Font.BOLD)
                    Dim headerFont As New Font(arabicFont, 12, Font.BOLD)
                    Dim cellFont As New Font(arabicFont, 10, Font.NORMAL)

                    ' إضافة العنوان
                    Dim titleParagraph As New Paragraph(title, titleFont)
                    titleParagraph.Alignment = Element.ALIGN_CENTER
                    titleParagraph.SpacingAfter = 20
                    document.Add(titleParagraph)

                    ' إضافة معلومات التقرير
                    Dim infoParagraph As New Paragraph($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}", cellFont)
                    infoParagraph.Alignment = Element.ALIGN_RIGHT
                    infoParagraph.SpacingAfter = 10
                    document.Add(infoParagraph)

                    ' إنشاء الجدول
                    Dim table As New PdfPTable(dataTable.Columns.Count)
                    table.WidthPercentage = 100
                    table.RunDirection = PdfWriter.RUN_DIRECTION_RTL

                    ' إضافة رؤوس الأعمدة
                    For Each column As DataColumn In dataTable.Columns
                        Dim headerCell As New PdfPCell(New Phrase(column.ColumnName, headerFont))
                        headerCell.BackgroundColor = BaseColor.LIGHT_GRAY
                        headerCell.HorizontalAlignment = Element.ALIGN_CENTER
                        headerCell.Padding = 5
                        table.AddCell(headerCell)
                    Next

                    ' إضافة البيانات
                    For Each row As DataRow In dataTable.Rows
                        For Each item In row.ItemArray
                            Dim cell As New PdfPCell(New Phrase(If(item Is DBNull.Value, "", item.ToString()), cellFont))
                            cell.HorizontalAlignment = Element.ALIGN_CENTER
                            cell.Padding = 3
                            table.AddCell(cell)
                        Next
                    Next

                    document.Add(table)

                    ' إضافة تذييل
                    Dim footerParagraph As New Paragraph("وزارة الشباب والرياضة - نظام المحاسبة", cellFont)
                    footerParagraph.Alignment = Element.ALIGN_CENTER
                    footerParagraph.SpacingBefore = 20
                    document.Add(footerParagraph)

                    document.Close()
                End Using

                Return fullPath

            Catch ex As Exception
                Throw New Exception($"خطأ في تصدير البيانات إلى PDF: {ex.Message}")
            End Try
        End Function

        ''' <summary>
        ''' إنشاء تقرير قسيمة راتب
        ''' </summary>
        ''' <param name="employeeData">بيانات الموظف</param>
        ''' <param name="payrollData">بيانات الراتب</param>
        ''' <returns>مسار ملف قسيمة الراتب</returns>
        Public Shared Function GeneratePayslip(employeeData As DataRow, payrollData As DataRow) As String
            Try
                Dim reportsPath As String = My.Settings.ReportsPath
                If Not Directory.Exists(reportsPath) Then
                    Directory.CreateDirectory(reportsPath)
                End If

                Dim fileName As String = $"قسيمة_راتب_{employeeData("EmployeeNumber")}_{DateTime.Now:yyyyMM}.pdf"
                Dim fullPath As String = Path.Combine(reportsPath, fileName)

                Using fs As New FileStream(fullPath, FileMode.Create)
                    Dim document As New Document(PageSize.A4, 25, 25, 30, 30)
                    Dim writer As PdfWriter = PdfWriter.GetInstance(document, fs)

                    document.Open()

                    ' إضافة خط عربي
                    Dim arabicFont As BaseFont
                    Try
                        arabicFont = BaseFont.CreateFont("c:\windows\fonts\arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED)
                    Catch
                        arabicFont = BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED)
                    End Try

                    Dim titleFont As New Font(arabicFont, 18, Font.BOLD)
                    Dim headerFont As New Font(arabicFont, 14, Font.BOLD)
                    Dim normalFont As New Font(arabicFont, 12, Font.NORMAL)

                    ' عنوان قسيمة الراتب
                    Dim title As New Paragraph("وزارة الشباب والرياضة", titleFont)
                    title.Alignment = Element.ALIGN_CENTER
                    document.Add(title)

                    Dim subtitle As New Paragraph("قسيمة راتب", headerFont)
                    subtitle.Alignment = Element.ALIGN_CENTER
                    subtitle.SpacingAfter = 20
                    document.Add(subtitle)

                    ' بيانات الموظف
                    Dim employeeTable As New PdfPTable(2)
                    employeeTable.WidthPercentage = 100
                    employeeTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL

                    AddTableRow(employeeTable, "الرقم الوظيفي:", employeeData("EmployeeNumber").ToString(), normalFont)
                    AddTableRow(employeeTable, "اسم الموظف:", $"{employeeData("FirstName")} {employeeData("MiddleName")} {employeeData("LastName")}", normalFont)
                    AddTableRow(employeeTable, "الشهر/السنة:", $"{payrollData("PayrollMonth")}/{payrollData("PayrollYear")}", normalFont)

                    document.Add(employeeTable)

                    ' مساحة فارغة
                    document.Add(New Paragraph(" ", normalFont))

                    ' تفاصيل الراتب
                    Dim salaryTable As New PdfPTable(2)
                    salaryTable.WidthPercentage = 100
                    salaryTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL

                    ' المخصصات
                    AddSectionHeader(salaryTable, "المخصصات", headerFont)
                    AddTableRow(salaryTable, "الراتب الأساسي:", FormatCurrency(payrollData("BasicSalary")), normalFont)
                    AddTableRow(salaryTable, "العلاوة:", FormatCurrency(payrollData("Allowance")), normalFont)
                    AddTableRow(salaryTable, "مخصص المنصب:", FormatCurrency(payrollData("PositionAllowance")), normalFont)
                    AddTableRow(salaryTable, "مخصص الشهادة:", FormatCurrency(payrollData("QualificationAllowance")), normalFont)
                    AddTableRow(salaryTable, "إجمالي المخصصات:", FormatCurrency(payrollData("TotalAllowances")), normalFont, True)

                    ' الاستقطاعات
                    AddSectionHeader(salaryTable, "الاستقطاعات", headerFont)
                    AddTableRow(salaryTable, "ضريبة الدخل:", FormatCurrency(payrollData("IncomeTax")), normalFont)
                    AddTableRow(salaryTable, "تقاعد الموظفين:", FormatCurrency(payrollData("EmployeeRetirement")), normalFont)
                    AddTableRow(salaryTable, "مساهمة الدائرة:", FormatCurrency(payrollData("DepartmentContribution")), normalFont)
                    AddTableRow(salaryTable, "إجمالي الاستقطاعات:", FormatCurrency(payrollData("TotalDeductions")), normalFont, True)

                    ' الإجمالي
                    AddSectionHeader(salaryTable, "الإجمالي", headerFont)
                    AddTableRow(salaryTable, "إجمالي الراتب:", FormatCurrency(payrollData("GrossSalary")), normalFont, True)
                    AddTableRow(salaryTable, "صافي الراتب:", FormatCurrency(payrollData("NetSalary")), normalFont, True)

                    document.Add(salaryTable)

                    ' تذييل
                    Dim footer As New Paragraph($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}", normalFont)
                    footer.Alignment = Element.ALIGN_CENTER
                    footer.SpacingBefore = 30
                    document.Add(footer)

                    document.Close()
                End Using

                Return fullPath

            Catch ex As Exception
                Throw New Exception($"خطأ في إنشاء قسيمة الراتب: {ex.Message}")
            End Try
        End Function

        ''' <summary>
        ''' إضافة صف إلى الجدول
        ''' </summary>
        Private Shared Sub AddTableRow(table As PdfPTable, label As String, value As String, font As Font, Optional isBold As Boolean = False)
            Dim labelCell As New PdfPCell(New Phrase(label, If(isBold, New Font(font.BaseFont, font.Size, Font.BOLD), font)))
            labelCell.BackgroundColor = If(isBold, BaseColor.LIGHT_GRAY, BaseColor.WHITE)
            labelCell.HorizontalAlignment = Element.ALIGN_RIGHT
            labelCell.Padding = 5
            table.AddCell(labelCell)

            Dim valueCell As New PdfPCell(New Phrase(value, If(isBold, New Font(font.BaseFont, font.Size, Font.BOLD), font)))
            valueCell.BackgroundColor = If(isBold, BaseColor.LIGHT_GRAY, BaseColor.WHITE)
            valueCell.HorizontalAlignment = Element.ALIGN_CENTER
            valueCell.Padding = 5
            table.AddCell(valueCell)
        End Sub

        ''' <summary>
        ''' إضافة رأس قسم
        ''' </summary>
        Private Shared Sub AddSectionHeader(table As PdfPTable, headerText As String, font As Font)
            Dim headerCell As New PdfPCell(New Phrase(headerText, font))
            headerCell.Colspan = 2
            headerCell.BackgroundColor = BaseColor.DARK_GRAY
            headerCell.HorizontalAlignment = Element.ALIGN_CENTER
            headerCell.Padding = 8
            table.AddCell(headerCell)
        End Sub

        ''' <summary>
        ''' تنسيق العملة
        ''' </summary>
        Private Shared Function FormatCurrency(amount As Object) As String
            If amount Is Nothing OrElse amount Is DBNull.Value Then
                Return "0.000"
            End If

            Dim decimalAmount As Decimal
            If Decimal.TryParse(amount.ToString(), decimalAmount) Then
                Return decimalAmount.ToString("N3", New CultureInfo("ar-SA"))
            End If

            Return "0.000"
        End Function

        ''' <summary>
        ''' فتح ملف التقرير
        ''' </summary>
        ''' <param name="filePath">مسار الملف</param>
        Public Shared Sub OpenReport(filePath As String)
            Try
                If File.Exists(filePath) Then
                    Process.Start(filePath)
                Else
                    Throw New FileNotFoundException("الملف غير موجود")
                End If
            Catch ex As Exception
                Throw New Exception($"خطأ في فتح التقرير: {ex.Message}")
            End Try
        End Sub

        ''' <summary>
        ''' حذف التقارير القديمة
        ''' </summary>
        ''' <param name="daysOld">عدد الأيام</param>
        Public Shared Sub CleanupOldReports(Optional daysOld As Integer = 30)
            Try
                Dim reportsPath As String = My.Settings.ReportsPath
                If Not Directory.Exists(reportsPath) Then
                    Return
                End If

                Dim cutoffDate As DateTime = DateTime.Now.AddDays(-daysOld)
                Dim files() As String = Directory.GetFiles(reportsPath, "*.*", SearchOption.TopDirectoryOnly)

                For Each file As String In files
                    Dim fileInfo As New FileInfo(file)
                    If fileInfo.CreationTime < cutoffDate Then
                        File.Delete(file)
                    End If
                Next

            Catch ex As Exception
                ' تجاهل أخطاء التنظيف
            End Try
        End Sub

    End Class

End Namespace
