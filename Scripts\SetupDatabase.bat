@echo off
echo ========================================
echo نظام محاسبة وزارة الشباب والرياضة
echo Ministry of Youth and Sports Accounting System
echo ========================================
echo.
echo إعداد قاعدة البيانات...
echo Setting up database...
echo.

REM التحقق من وجود SQL Server
echo التحقق من وجود SQL Server...
sqlcmd -S .\SQLEXPRESS -Q "SELECT @@VERSION" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: لم يتم العثور على SQL Server Express
    echo Error: SQL Server Express not found
    echo يرجى تثبيت SQL Server Express أولاً
    echo Please install SQL Server Express first
    pause
    exit /b 1
)

echo تم العثور على SQL Server بنجاح
echo SQL Server found successfully
echo.

REM إنشاء المجلدات المطلوبة
echo إنشاء المجلدات المطلوبة...
echo Creating required directories...

if not exist "C:\MinistryAccounting" mkdir "C:\MinistryAccounting"
if not exist "C:\MinistryAccounting\Database" mkdir "C:\MinistryAccounting\Database"
if not exist "C:\MinistryAccounting\Backups" mkdir "C:\MinistryAccounting\Backups"
if not exist "C:\MinistryAccounting\Reports" mkdir "C:\MinistryAccounting\Reports"
if not exist "C:\MinistryAccounting\Temp" mkdir "C:\MinistryAccounting\Temp"
if not exist "C:\MinistryAccounting\Logs" mkdir "C:\MinistryAccounting\Logs"

echo تم إنشاء المجلدات بنجاح
echo Directories created successfully
echo.

REM تشغيل سكريبت إنشاء قاعدة البيانات
echo تشغيل سكريبت إنشاء قاعدة البيانات...
echo Running database creation script...

sqlcmd -S .\SQLEXPRESS -i "CreateDatabase.sql"
if %ERRORLEVEL% NEQ 0 (
    echo خطأ في تشغيل سكريبت قاعدة البيانات
    echo Error running database script
    pause
    exit /b 1
)

echo تم إنشاء قاعدة البيانات بنجاح
echo Database created successfully
echo.

REM التحقق من إنشاء قاعدة البيانات
echo التحقق من إنشاء قاعدة البيانات...
echo Verifying database creation...

sqlcmd -S .\SQLEXPRESS -Q "SELECT name FROM sys.databases WHERE name = 'MinistryAccountingDB'" -h -1
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: لم يتم إنشاء قاعدة البيانات بشكل صحيح
    echo Error: Database was not created properly
    pause
    exit /b 1
)

echo تم التحقق من قاعدة البيانات بنجاح
echo Database verification successful
echo.

REM إنشاء ملف النسخ الاحتياطي
echo إنشاء ملف النسخ الاحتياطي التلقائي...
echo Creating automatic backup script...

(
echo @echo off
echo REM سكريبت النسخ الاحتياطي التلقائي لنظام محاسبة وزارة الشباب والرياضة
echo REM Automatic backup script for Ministry Accounting System
echo.
echo set BACKUP_PATH=C:\MinistryAccounting\Backups
echo set DB_NAME=MinistryAccountingDB
echo set DATE_STAMP=%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%%
echo set TIME_STAMP=%%time:~0,2%%%%time:~3,2%%%%time:~6,2%%
echo set TIME_STAMP=%%TIME_STAMP: =0%%
echo.
echo echo تشغيل النسخ الاحتياطي...
echo echo Running backup...
echo.
echo sqlcmd -S .\SQLEXPRESS -Q "BACKUP DATABASE [%%DB_NAME%%] TO DISK = '%%BACKUP_PATH%%\%%DB_NAME%%_%%DATE_STAMP%%_%%TIME_STAMP%%.bak' WITH FORMAT, INIT"
echo.
echo if %%ERRORLEVEL%% EQU 0 ^(
echo     echo تم إنشاء النسخة الاحتياطية بنجاح
echo     echo Backup completed successfully
echo ^) else ^(
echo     echo خطأ في إنشاء النسخة الاحتياطية
echo     echo Backup failed
echo ^)
) > "C:\MinistryAccounting\AutoBackup.bat"

echo تم إنشاء ملف النسخ الاحتياطي: C:\MinistryAccounting\AutoBackup.bat
echo Backup script created: C:\MinistryAccounting\AutoBackup.bat
echo.

REM إنشاء ملف استرجاع النسخة الاحتياطية
echo إنشاء ملف استرجاع النسخة الاحتياطية...
echo Creating restore script...

(
echo @echo off
echo REM سكريبت استرجاع النسخة الاحتياطية لنظام محاسبة وزارة الشباب والرياضة
echo REM Restore script for Ministry Accounting System
echo.
echo if "%%1"=="" ^(
echo     echo الاستخدام: RestoreBackup.bat [مسار_ملف_النسخة_الاحتياطية]
echo     echo Usage: RestoreBackup.bat [backup_file_path]
echo     exit /b 1
echo ^)
echo.
echo set BACKUP_FILE=%%1
echo set DB_NAME=MinistryAccountingDB
echo.
echo echo استرجاع النسخة الاحتياطية من: %%BACKUP_FILE%%
echo echo Restoring backup from: %%BACKUP_FILE%%
echo.
echo REM إغلاق الاتصالات النشطة
echo sqlcmd -S .\SQLEXPRESS -Q "ALTER DATABASE [%%DB_NAME%%] SET SINGLE_USER WITH ROLLBACK IMMEDIATE"
echo.
echo REM استرجاع النسخة الاحتياطية
echo sqlcmd -S .\SQLEXPRESS -Q "RESTORE DATABASE [%%DB_NAME%%] FROM DISK = '%%BACKUP_FILE%%' WITH REPLACE"
echo.
echo REM إعادة تفعيل الاتصالات المتعددة
echo sqlcmd -S .\SQLEXPRESS -Q "ALTER DATABASE [%%DB_NAME%%] SET MULTI_USER"
echo.
echo if %%ERRORLEVEL%% EQU 0 ^(
echo     echo تم استرجاع النسخة الاحتياطية بنجاح
echo     echo Restore completed successfully
echo ^) else ^(
echo     echo خطأ في استرجاع النسخة الاحتياطية
echo     echo Restore failed
echo ^)
) > "C:\MinistryAccounting\RestoreBackup.bat"

echo تم إنشاء ملف الاسترجاع: C:\MinistryAccounting\RestoreBackup.bat
echo Restore script created: C:\MinistryAccounting\RestoreBackup.bat
echo.

echo ========================================
echo تم إعداد النظام بنجاح!
echo System setup completed successfully!
echo ========================================
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo Default login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin123
echo.
echo يرجى تغيير كلمة المرور عند أول تسجيل دخول
echo Please change the password on first login
echo.
echo الملفات المنشأة:
echo Created files:
echo - قاعدة البيانات: MinistryAccountingDB
echo - Database: MinistryAccountingDB
echo - النسخ الاحتياطي: C:\MinistryAccounting\AutoBackup.bat
echo - Backup script: C:\MinistryAccounting\AutoBackup.bat
echo - الاسترجاع: C:\MinistryAccounting\RestoreBackup.bat
echo - Restore script: C:\MinistryAccounting\RestoreBackup.bat
echo.
pause
