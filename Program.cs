using System;
using System.Drawing;
using System.Globalization;
using System.Threading;
using System.Windows.Forms;

namespace SimpleMinistryApp
{
    /// <summary>
    /// نسخة مبسطة من نظام محاسبة وزارة الشباب والرياضة للعرض التوضيحي
    /// </summary>
    public static class Program
    {
        /// <summary>
        /// نقطة دخول التطبيق
        /// </summary>
        [STAThread]
        public static void Main()
        {
            // تعيين الثقافة العربية
            var arabicCulture = new CultureInfo("ar-SA");
            Thread.CurrentThread.CurrentCulture = arabicCulture;
            Thread.CurrentThread.CurrentUICulture = arabicCulture;

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // عرض نموذج تسجيل الدخول
            var loginForm = new SimpleLoginForm();
            if (loginForm.ShowDialog() == DialogResult.OK)
            {
                // إذا تم تسجيل الدخول بنجاح، عرض النموذج الرئيسي
                Application.Run(new SimpleMainForm(loginForm.LoggedInUser));
            }
        }
    }

    /// <summary>
    /// نموذج تسجيل الدخول المبسط
    /// </summary>
    public partial class SimpleLoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblTitle;
        private Label lblUsername;
        private Label lblPassword;

        public string LoggedInUser { get; private set; }

        public SimpleLoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            Text = "تسجيل الدخول - نظام محاسبة وزارة الشباب والرياضة";
            Size = new Size(400, 300);
            StartPosition = FormStartPosition.CenterScreen;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label
            {
                Text = "نظام محاسبة وزارة الشباب والرياضة",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(50, 30),
                Size = new Size(300, 30)
            };

            // تسمية اسم المستخدم
            lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 10),
                Location = new Point(280, 80),
                Size = new Size(100, 20)
            };

            // حقل اسم المستخدم
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(50, 80),
                Size = new Size(220, 25),
                Text = "admin"
            };

            // تسمية كلمة المرور
            lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 10),
                Location = new Point(280, 120),
                Size = new Size(100, 20)
            };

            // حقل كلمة المرور
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 10),
                UseSystemPasswordChar = true,
                Location = new Point(50, 120),
                Size = new Size(220, 25),
                Text = "admin123"
            };

            // زر تسجيل الدخول
            btnLogin = new Button
            {
                Text = "تسجيل الدخول",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                BackColor = Color.LightBlue,
                Location = new Point(180, 170),
                Size = new Size(100, 35)
            };
            btnLogin.Click += BtnLogin_Click;

            // زر الإلغاء
            btnCancel = new Button
            {
                Text = "إلغاء",
                Font = new Font("Tahoma", 10),
                BackColor = Color.LightGray,
                Location = new Point(70, 170),
                Size = new Size(100, 35)
            };
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للنموذج
            Controls.AddRange(new Control[] { lblTitle, lblUsername, txtUsername, lblPassword, txtPassword, btnLogin, btnCancel });

            AcceptButton = btnLogin;
            CancelButton = btnCancel;
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return;
            }

            // التحقق من بيانات تسجيل الدخول
            if (txtUsername.Text == "admin" && txtPassword.Text == "admin123")
            {
                LoggedInUser = txtUsername.Text;
                MessageBox.Show("تم تسجيل الدخول بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.OK;
                Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtPassword.Text = "";
                txtPassword.Focus();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }

    /// <summary>
    /// النموذج الرئيسي المبسط
    /// </summary>
    public partial class SimpleMainForm : Form
    {
        private readonly string currentUser;
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private Label lblWelcome;

        public SimpleMainForm(string user)
        {
            currentUser = user;
            InitializeComponent();
            SetupMenu();
            SetupStatusBar();
        }

        private void InitializeComponent()
        {
            Text = $"نظام محاسبة وزارة الشباب والرياضة - {currentUser}";
            WindowState = FormWindowState.Maximized;
            IsMdiContainer = true;
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            BackColor = Color.LightSteelBlue;

            // رسالة الترحيب
            lblWelcome = new Label
            {
                Text = $"مرحباً بك في نظام محاسبة وزارة الشباب والرياضة\n\nالمستخدم: {currentUser}\nالتاريخ: {DateTime.Now:yyyy/MM/dd}\nالوقت: {DateTime.Now:HH:mm:ss}",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            Controls.Add(lblWelcome);
        }

        private void SetupMenu()
        {
            menuStrip = new MenuStrip { RightToLeft = RightToLeft.Yes };

            // قائمة إدارة المستخدمين
            var usersMenu = new ToolStripMenuItem("إدارة المستخدمين");
            usersMenu.DropDownItems.Add("المستخدمون", null, (s, e) => ShowModuleForm("إدارة المستخدمين", "هنا يتم إدارة المستخدمين وصلاحياتهم"));
            usersMenu.DropDownItems.Add("مجموعات المستخدمين", null, (s, e) => ShowModuleForm("مجموعات المستخدمين", "هنا يتم إدارة مجموعات المستخدمين"));
            usersMenu.DropDownItems.Add("الصلاحيات", null, (s, e) => ShowModuleForm("الصلاحيات", "هنا يتم إدارة صلاحيات النظام"));

            // قائمة الإعدادات
            var settingsMenu = new ToolStripMenuItem("الإعدادات");
            settingsMenu.DropDownItems.Add("معلومات المؤسسة", null, (s, e) => ShowModuleForm("معلومات المؤسسة", "هنا يتم إدارة معلومات وزارة الشباب والرياضة"));
            settingsMenu.DropDownItems.Add("الدوائر", null, (s, e) => ShowModuleForm("الدوائر", "هنا يتم إدارة دوائر الوزارة"));
            settingsMenu.DropDownItems.Add("الأقسام", null, (s, e) => ShowModuleForm("الأقسام", "هنا يتم إدارة أقسام الدوائر"));
            settingsMenu.DropDownItems.Add("المصارف", null, (s, e) => ShowModuleForm("المصارف", "هنا يتم إدارة المصارف وفروعها"));

            // قائمة المحاسبة
            var accountingMenu = new ToolStripMenuItem("المحاسبة");
            accountingMenu.DropDownItems.Add("دليل الحسابات", null, (s, e) => ShowModuleForm("دليل الحسابات", "هنا يتم إدارة شجرة الحسابات المحاسبية"));
            accountingMenu.DropDownItems.Add("القيود اليومية", null, (s, e) => ShowModuleForm("القيود اليومية", "هنا يتم إدخال وإدارة القيود المحاسبية"));
            accountingMenu.DropDownItems.Add("سندات القبض", null, (s, e) => ShowModuleForm("سندات القبض", "هنا يتم إدارة سندات القبض"));
            accountingMenu.DropDownItems.Add("سندات الصرف", null, (s, e) => ShowModuleForm("سندات الصرف", "هنا يتم إدارة سندات الصرف"));

            // قائمة الرواتب
            var payrollMenu = new ToolStripMenuItem("الرواتب");
            payrollMenu.DropDownItems.Add("الموظفون", null, (s, e) => ShowModuleForm("الموظفون", "هنا يتم إدارة بيانات الموظفين"));
            payrollMenu.DropDownItems.Add("المناصب", null, (s, e) => ShowModuleForm("المناصب", "هنا يتم إدارة المناصب الوظيفية"));
            payrollMenu.DropDownItems.Add("معالجة الرواتب", null, (s, e) => ShowModuleForm("معالجة الرواتب", "هنا يتم حساب ومعالجة رواتب الموظفين"));

            // قائمة التقارير
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.Add("التقارير المالية", null, (s, e) => ShowModuleForm("التقارير المالية", "هنا يتم عرض التقارير المالية والمحاسبية"));
            reportsMenu.DropDownItems.Add("تقارير الرواتب", null, (s, e) => ShowModuleForm("تقارير الرواتب", "هنا يتم عرض تقارير الرواتب والمخصصات"));

            // قائمة المساعدة
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, ShowAbout);
            helpMenu.DropDownItems.Add("دليل المستخدم", null, ShowUserGuide);

            menuStrip.Items.AddRange(new ToolStripItem[] { usersMenu, settingsMenu, accountingMenu, payrollMenu, reportsMenu, helpMenu });
            MainMenuStrip = menuStrip;
            Controls.Add(menuStrip);
        }

        private void SetupStatusBar()
        {
            statusStrip = new StatusStrip { RightToLeft = RightToLeft.Yes };

            var statusLabel = new ToolStripStatusLabel("جاهز");
            var userLabel = new ToolStripStatusLabel($"المستخدم: {currentUser}");
            var dateLabel = new ToolStripStatusLabel($"التاريخ: {DateTime.Now:yyyy/MM/dd}");
            var timeLabel = new ToolStripStatusLabel($"الوقت: {DateTime.Now:HH:mm:ss}");

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, userLabel, dateLabel, timeLabel });
            Controls.Add(statusStrip);
        }

        private void ShowAbout(object sender, EventArgs e)
        {
            MessageBox.Show("نظام محاسبة وزارة الشباب والرياضة\n" +
                           "الإصدار 1.0.0\n" +
                           "تم التطوير باستخدام C# و .NET\n" +
                           "جميع الحقوق محفوظة © 2024",
                           "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowUserGuide(object sender, EventArgs e)
        {
            MessageBox.Show("دليل المستخدم سيكون متاحاً قريباً", "دليل المستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowModuleForm(string title, string description)
        {
            var moduleForm = new Form
            {
                Text = title,
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            var lblDescription = new Label
            {
                Text = description,
                Font = new Font("Tahoma", 12),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill,
                ForeColor = Color.DarkBlue
            };

            moduleForm.Controls.Add(lblDescription);
            moduleForm.ShowDialog(this);
        }
    }
}
