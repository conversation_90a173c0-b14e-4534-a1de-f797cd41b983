<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  
  <connectionStrings>
    <add name="MinistryAccountingDB" 
         connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=MinistryAccountingDB;Integrated Security=True;MultipleActiveResultSets=True;Application Name=MinistryAccountingSystem" 
         providerName="System.Data.SqlClient" />
  </connectionStrings>
  
  <appSettings>
    <!-- إعدادات التطبيق العامة -->
    <add key="OrganizationName" value="وزارة الشباب والرياضة" />
    <add key="OrganizationNameEn" value="Ministry of Youth and Sports" />
    <add key="SystemVersion" value="1.0.0" />
    <add key="SupportRTL" value="true" />
    <add key="DefaultLanguage" value="ar-SA" />
    
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="BackupPath" value="C:\MinistryAccounting\Backups\" />
    <add key="AutoBackupEnabled" value="true" />
    <add key="AutoBackupInterval" value="24" />
    
    <!-- إعدادات التقارير -->
    <add key="ReportsPath" value="C:\MinistryAccounting\Reports\" />
    <add key="TempPath" value="C:\MinistryAccounting\Temp\" />
    
    <!-- إعدادات الأمان -->
    <add key="PasswordMinLength" value="8" />
    <add key="SessionTimeoutMinutes" value="30" />
    <add key="MaxLoginAttempts" value="3" />
    
    <!-- إعدادات المحاسبة -->
    <add key="FiscalYearStart" value="01/01" />
    <add key="DefaultCurrency" value="IQD" />
    <add key="DecimalPlaces" value="3" />
  </appSettings>
  
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
